<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Group Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 500;
        }
        .btn-success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 500;
        }
        .btn-warning {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 500;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background-color: #28a745; }
        .status-disconnected { background-color: #dc3545; }
        .loading {
            display: none;
        }
        .member-count {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            .card {
                margin-bottom: 15px;
            }
            .btn {
                width: 100%;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">
                            <i class="fab fa-telegram-plane me-2"></i>
                            Telegram Group Manager
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>
                                    <span id="status-indicator" class="status-indicator status-disconnected"></span>
                                    Connection Status
                                </h5>
                                <p id="status-text" class="text-muted">Disconnected</p>
                            </div>
                            <div class="col-md-6 text-end">
                                <button id="connect-btn" class="btn btn-primary">
                                    <i class="fas fa-plug me-2"></i>Connect
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="main-interface" style="display: none;">
            <!-- Member Extraction Section -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i class="fas fa-users me-2"></i>
                                Extract Group Members
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label class="form-label">Group Username or ID</label>
                                        <input type="text" id="source-group" class="form-control" 
                                               placeholder="@groupusername or group ID">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Limit (optional)</label>
                                        <input type="number" id="member-limit" class="form-control" 
                                               placeholder="Leave empty for all">
                                    </div>
                                </div>
                            </div>
                            <button id="extract-btn" class="btn btn-success">
                                <i class="fas fa-download me-2"></i>Extract Members
                            </button>
                            <div id="extract-loading" class="loading mt-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Extracting members...</span>
                            </div>
                            <div id="extracted-info" class="mt-3" style="display: none;">
                                <span class="member-count">
                                    <i class="fas fa-users me-1"></i>
                                    <span id="member-count">0</span> members extracted
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Member Transfer Section -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i class="fas fa-share me-2"></i>
                                Transfer Members
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label class="form-label">Target Group</label>
                                        <input type="text" id="target-group" class="form-control" 
                                               placeholder="@targetgroup or group ID">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Delay (seconds)</label>
                                        <input type="number" id="transfer-delay" class="form-control" 
                                               value="2" min="1" max="10">
                                    </div>
                                </div>
                            </div>
                            <button id="transfer-btn" class="btn btn-warning">
                                <i class="fas fa-arrow-right me-2"></i>Transfer Members
                            </button>
                            <div id="transfer-loading" class="loading mt-3">
                                <div class="spinner-border text-warning" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Transferring members...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bulk Messaging Section -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i class="fas fa-envelope me-2"></i>
                                Bulk Messaging
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Message</label>
                                <textarea id="bulk-message" class="form-control" rows="4" 
                                          placeholder="Enter your message here..."></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Target Type</label>
                                        <select id="target-type" class="form-select" onchange="toggleTargetOptions()">
                                            <option value="users">Send to Users</option>
                                            <option value="groups">Send to Groups</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4" id="recipients-section">
                                    <div class="mb-3">
                                        <label class="form-label">Recipients</label>
                                        <select id="recipients-type" class="form-select">
                                            <option value="extracted">Use Extracted Members</option>
                                            <option value="custom">Custom Recipients</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Delay (seconds)</label>
                                        <input type="number" id="message-delay" class="form-control"
                                               value="1" min="1" max="10">
                                    </div>
                                </div>
                            </div>

                            <!-- Custom Recipients Input (for users) -->
                            <div id="custom-recipients-section" class="mb-3" style="display: none;">
                                <label class="form-label">Custom User Recipients</label>
                                <textarea id="custom-recipients" class="form-control" rows="3"
                                          placeholder="Enter usernames or user IDs, one per line (e.g., @username or 123456789)"></textarea>
                                <small class="form-text text-muted">Enter usernames (with @) or user IDs, one per line</small>
                            </div>

                            <!-- Group Identifiers Input (for groups) -->
                            <div id="group-identifiers-section" class="mb-3" style="display: none;">
                                <label class="form-label">Group Identifiers</label>
                                <textarea id="group-identifiers" class="form-control" rows="3"
                                          placeholder="Enter group usernames, IDs, or invite links, one per line (e.g., @groupname, -123456789, or https://t.me/groupname)"></textarea>
                                <small class="form-text text-muted">Enter group usernames (with @), group IDs, or invite links, one per line</small>
                            </div>

                            <div class="d-flex gap-2">
                                <button id="send-bulk-btn" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>Send to Users
                                </button>
                                <button id="send-groups-btn" class="btn btn-success" style="display: none;">
                                    <i class="fas fa-users me-2"></i>Send to Groups
                                </button>
                            </div>
                            <div id="message-loading" class="loading mt-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Sending messages...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Modal -->
        <div class="modal fade" id="resultsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Operation Results</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="results-content"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='app.js') }}"></script>
    <script>
        const app = new TelegramApp();

        function toggleTargetOptions() {
            const targetType = document.getElementById('target-type').value;
            const recipientsSection = document.getElementById('recipients-section');
            const customRecipientsSection = document.getElementById('custom-recipients-section');
            const groupIdentifiersSection = document.getElementById('group-identifiers-section');
            const sendBulkBtn = document.getElementById('send-bulk-btn');
            const sendGroupsBtn = document.getElementById('send-groups-btn');
            const delayInput = document.getElementById('message-delay');

            if (targetType === 'users') {
                recipientsSection.style.display = 'block';
                customRecipientsSection.style.display = 'none';
                groupIdentifiersSection.style.display = 'none';
                sendBulkBtn.style.display = 'inline-block';
                sendGroupsBtn.style.display = 'none';
                sendBulkBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Send to Users';
                delayInput.value = '1';
            } else {
                recipientsSection.style.display = 'none';
                customRecipientsSection.style.display = 'none';
                groupIdentifiersSection.style.display = 'block';
                sendBulkBtn.style.display = 'none';
                sendGroupsBtn.style.display = 'inline-block';
                delayInput.value = '2';
            }
        }

        // Show/hide custom recipients based on selection
        document.getElementById('recipients-type').addEventListener('change', function() {
            const customRecipientsSection = document.getElementById('custom-recipients-section');
            if (this.value === 'custom') {
                customRecipientsSection.style.display = 'block';
            } else {
                customRecipientsSection.style.display = 'none';
            }
        });
    </script>
</body>
</html>
