import asyncio
import time
from telethon import TelegramClient, events
from telethon.errors import SessionPasswordNeededError, FloodWaitError, UserPrivacyRestrictedError
from telethon.tl.functions.channels import InviteToChannelRequest, GetParticipantsRequest
from telethon.tl.functions.messages import AddChatUserRequest
from telethon.tl.types import ChannelParticipantsSearch, InputPeerEmpty
from config import Config
from logger import logger
import json

class TelegramAutomation:
    def __init__(self):
        self.client = None
        self.is_authenticated = False
        
    async def initialize(self):
        """Initialize the Telegram client"""
        try:
            Config.validate()
            self.client = TelegramClient(
                f"sessions/{Config.SESSION_NAME}",
                Config.API_ID,
                Config.API_HASH
            )

            # Connect first
            await self.client.connect()
            logger.info("Connected to Telegram servers")

            # Check if already authorized
            if await self.client.is_user_authorized():
                logger.info("Already authorized")
                self.is_authenticated = True
                return True

            # Send code request
            logger.info("Sending code request...")
            await self.client.send_code_request(Config.PHONE_NUMBER)
            logger.info("Code request sent successfully")

            # For now, we'll return True and handle code input separately
            # The actual authorization will happen when user provides the code
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Telegram client: {e}")
            return False

    async def verify_code(self, code):
        """Verify the authentication code"""
        try:
            if not self.client:
                raise Exception("Client not initialized")

            await self.client.sign_in(Config.PHONE_NUMBER, code)
            self.is_authenticated = True
            logger.info("Successfully authenticated with Telegram")
            return True

        except Exception as e:
            logger.error(f"Failed to verify code: {e}")
            return False
    
    async def get_group_members(self, group_identifier, limit=None):
        """Extract members from a group"""
        if not self.is_authenticated:
            raise Exception("Client not authenticated")
        
        try:
            # Get the group entity
            group = await self.client.get_entity(group_identifier)
            logger.info(f"Extracting members from group: {group.title}")
            
            members = []
            offset = 0
            batch_size = 200
            
            while True:
                try:
                    participants = await self.client(GetParticipantsRequest(
                        group,
                        ChannelParticipantsSearch(''),
                        offset,
                        batch_size,
                        hash=0
                    ))
                    
                    if not participants.users:
                        break
                    
                    for user in participants.users:
                        if not user.bot and not user.deleted:
                            member_info = {
                                'id': user.id,
                                'username': user.username,
                                'first_name': user.first_name,
                                'last_name': user.last_name,
                                'phone': user.phone,
                                'access_hash': user.access_hash
                            }
                            members.append(member_info)
                    
                    offset += len(participants.users)
                    
                    if limit and len(members) >= limit:
                        members = members[:limit]
                        break
                    
                    # Rate limiting
                    await asyncio.sleep(1)
                    
                except FloodWaitError as e:
                    logger.warning(f"Rate limited, waiting {e.seconds} seconds")
                    await asyncio.sleep(e.seconds)
                    continue
                except Exception as e:
                    logger.error(f"Error getting participants: {e}")
                    break
            
            logger.info(f"Extracted {len(members)} members from {group.title}")
            return members
            
        except Exception as e:
            logger.error(f"Failed to get group members: {e}")
            raise
    
    async def add_members_to_group(self, target_group_identifier, members, delay=2):
        """Add members to a target group"""
        if not self.is_authenticated:
            raise Exception("Client not authenticated")
        
        try:
            target_group = await self.client.get_entity(target_group_identifier)
            logger.info(f"Adding members to group: {target_group.title}")
            
            added_count = 0
            failed_count = 0
            
            for member in members:
                try:
                    # Try to add the user
                    user_to_add = await self.client.get_entity(member['id'])
                    
                    if hasattr(target_group, 'megagroup') and target_group.megagroup:
                        # For channels/supergroups
                        await self.client(InviteToChannelRequest(
                            target_group,
                            [user_to_add]
                        ))
                    else:
                        # For regular groups
                        await self.client(AddChatUserRequest(
                            target_group.id,
                            user_to_add,
                            fwd_limit=10
                        ))
                    
                    added_count += 1
                    logger.info(f"Added user {member.get('username', member['id'])} to group")
                    
                    # Rate limiting
                    await asyncio.sleep(delay)
                    
                except FloodWaitError as e:
                    logger.warning(f"Rate limited, waiting {e.seconds} seconds")
                    await asyncio.sleep(e.seconds)
                    continue
                except UserPrivacyRestrictedError:
                    logger.warning(f"User {member.get('username', member['id'])} has privacy restrictions")
                    failed_count += 1
                except Exception as e:
                    logger.error(f"Failed to add user {member.get('username', member['id'])}: {e}")
                    failed_count += 1
                    continue
            
            logger.info(f"Member addition complete. Added: {added_count}, Failed: {failed_count}")
            return {"added": added_count, "failed": failed_count}
            
        except Exception as e:
            logger.error(f"Failed to add members to group: {e}")
            raise

    async def send_bulk_messages(self, recipients, message, delay=1):
        """Send bulk messages to a list of users"""
        if not self.is_authenticated:
            raise Exception("Client not authenticated")

        try:
            logger.info(f"Starting bulk message send to {len(recipients)} recipients")

            sent_count = 0
            failed_count = 0

            for recipient in recipients:
                try:
                    # Get the user entity
                    if isinstance(recipient, dict):
                        user = await self.client.get_entity(recipient['id'])
                    else:
                        user = await self.client.get_entity(recipient)

                    # Send the message
                    await self.client.send_message(user, message)
                    sent_count += 1

                    logger.info(f"Message sent to {getattr(user, 'username', user.id)}")

                    # Rate limiting
                    await asyncio.sleep(delay)

                except FloodWaitError as e:
                    logger.warning(f"Rate limited, waiting {e.seconds} seconds")
                    await asyncio.sleep(e.seconds)
                    continue
                except UserPrivacyRestrictedError:
                    logger.warning(f"User has privacy restrictions, skipping")
                    failed_count += 1
                except Exception as e:
                    logger.error(f"Failed to send message to recipient: {e}")
                    failed_count += 1
                    continue

            logger.info(f"Bulk messaging complete. Sent: {sent_count}, Failed: {failed_count}")
            return {"sent": sent_count, "failed": failed_count}

        except Exception as e:
            logger.error(f"Failed to send bulk messages: {e}")
            raise

    async def get_my_groups(self):
        """Get list of groups the user is admin of"""
        if not self.is_authenticated:
            raise Exception("Client not authenticated")

        try:
            dialogs = await self.client.get_dialogs()
            my_groups = []

            for dialog in dialogs:
                if dialog.is_group or dialog.is_channel:
                    entity = dialog.entity
                    # Check if user has admin rights
                    try:
                        permissions = await self.client.get_permissions(entity, 'me')
                        if permissions.is_admin or permissions.is_creator:
                            group_info = {
                                'id': entity.id,
                                'title': entity.title,
                                'username': getattr(entity, 'username', None),
                                'type': 'channel' if dialog.is_channel else 'group',
                                'members_count': getattr(entity, 'participants_count', 0)
                            }
                            my_groups.append(group_info)
                    except:
                        continue

            logger.info(f"Found {len(my_groups)} groups where user has admin rights")
            return my_groups

        except Exception as e:
            logger.error(f"Failed to get user groups: {e}")
            raise

    async def disconnect(self):
        """Disconnect the client"""
        if self.client:
            await self.client.disconnect()
            logger.info("Telegram client disconnected")
