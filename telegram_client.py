import asyncio
import time
from telethon import TelegramClient, events
from telethon.errors import SessionPasswordNeededError, FloodWaitError, UserPrivacyRestrictedError, ChatWriteForbiddenError, ChatAdminRequiredError
from telethon.tl.functions.channels import InviteToChannelRequest, GetParticipantsRequest
from telethon.tl.functions.messages import AddChatUserRequest
from telethon.tl.types import ChannelParticipantsSearch, InputPeerEmpty, ChannelParticipantsRecent, ChannelParticipantsAdmins, User, Channel
from config import Config
from logger import logger
import json

class TelegramAutomation:
    def __init__(self):
        self.client = None
        self.is_authenticated = False
        
    async def initialize(self):
        """Initialize the Telegram client"""
        try:
            Config.validate()
            self.client = TelegramClient(
                f"sessions/{Config.SESSION_NAME}",
                Config.API_ID,
                Config.API_HASH
            )

            # Connect first
            await self.client.connect()
            logger.info("Connected to Telegram servers")

            # Check if already authorized
            if await self.client.is_user_authorized():
                logger.info("Already authorized")
                self.is_authenticated = True
                return True

            # Send code request
            logger.info("Sending code request...")
            await self.client.send_code_request(Config.PHONE_NUMBER)
            logger.info("Code request sent successfully")

            # For now, we'll return True and handle code input separately
            # The actual authorization will happen when user provides the code
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Telegram client: {e}")
            return False

    async def verify_code(self, code):
        """Verify the authentication code"""
        try:
            if not self.client:
                raise Exception("Client not initialized")

            await self.client.sign_in(Config.PHONE_NUMBER, code)
            self.is_authenticated = True
            logger.info("Successfully authenticated with Telegram")
            return True

        except Exception as e:
            logger.error(f"Failed to verify code: {e}")
            return False
    
    async def get_group_members(self, group_identifier, limit=None):
        """Extract members from a group using multiple methods"""
        if not self.is_authenticated:
            raise Exception("Client not authenticated")

        try:
            # Get the group entity
            group = await self.client.get_entity(group_identifier)
            logger.info(f"Extracting members from group: {group.title}")

            members = []

            # Method 1: Try the standard GetParticipantsRequest (requires admin)
            try:
                logger.info("Attempting admin method (GetParticipantsRequest)...")
                members = await self._extract_with_admin_method(group, limit)
                if members:
                    logger.info(f"Successfully extracted {len(members)} members using admin method")
                    return members
            except Exception as e:
                logger.warning(f"Admin method failed: {e}")

            # Method 2: Try iter_participants with aggressive mode (may work for non-admins)
            try:
                logger.info("Attempting aggressive method (iter_participants)...")
                members = await self._extract_with_aggressive_method(group, limit)
                if members:
                    logger.info(f"Successfully extracted {len(members)} members using aggressive method")
                    return members
            except Exception as e:
                logger.warning(f"Aggressive method failed: {e}")

            # Method 3: Try alternative approach for public groups
            try:
                logger.info("Attempting public group method...")
                members = await self._extract_with_public_method(group, limit)
                if members:
                    logger.info(f"Successfully extracted {len(members)} members using public method")
                    return members
            except Exception as e:
                logger.warning(f"Public method failed: {e}")

            # Method 4: Try extracting from message history (may work for non-admins)
            try:
                logger.info("Attempting message history method...")
                members = await self._extract_from_message_history(group, limit)
                if members:
                    logger.info(f"Successfully extracted {len(members)} members using message history method")
                    return members
            except Exception as e:
                logger.warning(f"Message history method failed: {e}")

            # Method 5: Try extracting from recent activity
            try:
                logger.info("Attempting recent activity method...")
                members = await self._extract_from_recent_activity(group, limit)
                if members:
                    logger.info(f"Successfully extracted {len(members)} members using recent activity method")
                    return members
            except Exception as e:
                logger.warning(f"Recent activity method failed: {e}")

            # Method 6: Try extracting admins (works for any group type)
            try:
                logger.info("Attempting admin extraction method...")
                members = await self._extract_admins(group, limit)
                if members:
                    logger.info(f"Successfully extracted {len(members)} admins")
                    return members
            except Exception as e:
                logger.warning(f"Admin extraction failed: {e}")

            # Method 7: Try extracting from message reactions/views (for channels)
            try:
                logger.info("Attempting reaction/view method for channels...")
                members = await self._extract_from_reactions(group, limit)
                if members:
                    logger.info(f"Successfully extracted {len(members)} members using reaction method")
                    return members
            except Exception as e:
                logger.warning(f"Reaction method failed: {e}")

            # Method 8: Try extracting from comments (for channels with comments enabled)
            try:
                logger.info("Attempting comment extraction method...")
                members = await self._extract_from_comments(group, limit)
                if members:
                    logger.info(f"Successfully extracted {len(members)} members from comments")
                    return members
            except Exception as e:
                logger.warning(f"Comment extraction failed: {e}")

            # Method 9: Try alternative channel exploration
            try:
                logger.info("Attempting alternative channel exploration...")
                members = await self._extract_from_channel_exploration(group, limit)
                if members:
                    logger.info(f"Successfully extracted {len(members)} members from channel exploration")
                    return members
            except Exception as e:
                logger.warning(f"Channel exploration failed: {e}")

            logger.warning("All extraction methods failed")
            logger.info("Suggestion: Try a different group with more user interaction (discussion groups rather than broadcast channels)")
            return []

        except Exception as e:
            logger.error(f"Failed to get group members: {e}")
            raise

    async def _extract_with_admin_method(self, group, limit=None):
        """Original method using GetParticipantsRequest (requires admin privileges)"""
        members = []
        offset = 0
        batch_size = 200

        while True:
            try:
                participants = await self.client(GetParticipantsRequest(
                    group,
                    ChannelParticipantsSearch(''),
                    offset,
                    batch_size,
                    hash=0
                ))

                if not participants.users:
                    break

                for user in participants.users:
                    if not user.bot and not user.deleted:
                        member_info = {
                            'id': user.id,
                            'username': user.username,
                            'first_name': user.first_name,
                            'last_name': user.last_name,
                            'phone': user.phone,
                            'access_hash': user.access_hash
                        }
                        members.append(member_info)

                offset += len(participants.users)

                if limit and len(members) >= limit:
                    members = members[:limit]
                    break

                # Rate limiting
                await asyncio.sleep(1)

            except FloodWaitError as e:
                logger.warning(f"Rate limited, waiting {e.seconds} seconds")
                await asyncio.sleep(e.seconds)
                continue
            except Exception as e:
                logger.error(f"Error in admin method: {e}")
                raise

        return members

    async def _extract_with_aggressive_method(self, group, limit=None):
        """Alternative method using iter_participants with aggressive mode"""
        members = []
        count = 0

        try:
            # Use iter_participants with aggressive mode
            async for user in self.client.iter_participants(group, aggressive=True):
                if not user.bot and not user.deleted:
                    member_info = {
                        'id': user.id,
                        'username': user.username,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'phone': user.phone,
                        'access_hash': user.access_hash
                    }
                    members.append(member_info)
                    count += 1

                    if limit and count >= limit:
                        break

                    # Rate limiting to avoid flood errors
                    if count % 100 == 0:
                        await asyncio.sleep(2)
                        logger.info(f"Extracted {count} members so far...")

        except Exception as e:
            logger.error(f"Error in aggressive method: {e}")
            raise

        return members

    async def _extract_with_public_method(self, group, limit=None):
        """Alternative method for public groups/channels"""
        members = []

        try:
            # Try different participant filters for public groups
            from telethon.tl.types import ChannelParticipantsRecent, ChannelParticipantsAdmins

            filters = [
                ChannelParticipantsRecent(),
                ChannelParticipantsSearch(''),
            ]

            for filter_type in filters:
                try:
                    offset = 0
                    batch_size = min(100, limit if limit else 100)  # Smaller batches for public method

                    while True:
                        participants = await self.client(GetParticipantsRequest(
                            group,
                            filter_type,
                            offset,
                            batch_size,
                            hash=0
                        ))

                        if not participants.users:
                            break

                        for user in participants.users:
                            if not user.bot and not user.deleted:
                                member_info = {
                                    'id': user.id,
                                    'username': user.username,
                                    'first_name': user.first_name,
                                    'last_name': user.last_name,
                                    'phone': user.phone,
                                    'access_hash': user.access_hash
                                }
                                members.append(member_info)

                        offset += len(participants.users)

                        if limit and len(members) >= limit:
                            members = members[:limit]
                            return members

                        # Rate limiting
                        await asyncio.sleep(2)

                        # Break if we got fewer users than requested (end of list)
                        if len(participants.users) < batch_size:
                            break

                    if members:  # If we got some members with this filter, return them
                        return members

                except Exception as e:
                    logger.warning(f"Filter {filter_type} failed: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error in public method: {e}")
            raise

        return members

    async def check_admin_privileges(self, group_identifier):
        """Check if the current user has admin privileges in the group"""
        try:
            group = await self.client.get_entity(group_identifier)
            me = await self.client.get_me()

            # Get the participant info for the current user
            participant = await self.client(GetParticipantsRequest(
                group,
                ChannelParticipantsSearch(''),
                0,
                1,
                hash=0
            ))

            # Check if we can access participants (indicates admin privileges)
            return True

        except Exception as e:
            if "admin privileges are required" in str(e).lower():
                return False
            logger.warning(f"Could not determine admin status: {e}")
            return False
    
    async def add_members_to_group(self, target_group_identifier, members, delay=2):
        """Add members to a target group"""
        if not self.is_authenticated:
            raise Exception("Client not authenticated")
        
        try:
            target_group = await self.client.get_entity(target_group_identifier)
            logger.info(f"Adding members to group: {target_group.title}")
            
            added_count = 0
            failed_count = 0
            
            for member in members:
                try:
                    # Try to add the user
                    user_to_add = await self.client.get_entity(member['id'])
                    
                    if hasattr(target_group, 'megagroup') and target_group.megagroup:
                        # For channels/supergroups
                        await self.client(InviteToChannelRequest(
                            target_group,
                            [user_to_add]
                        ))
                    else:
                        # For regular groups
                        await self.client(AddChatUserRequest(
                            target_group.id,
                            user_to_add,
                            fwd_limit=10
                        ))
                    
                    added_count += 1
                    logger.info(f"Added user {member.get('username', member['id'])} to group")
                    
                    # Rate limiting
                    await asyncio.sleep(delay)
                    
                except FloodWaitError as e:
                    logger.warning(f"Rate limited, waiting {e.seconds} seconds")
                    await asyncio.sleep(e.seconds)
                    continue
                except UserPrivacyRestrictedError:
                    logger.warning(f"User {member.get('username', member['id'])} has privacy restrictions")
                    failed_count += 1
                except Exception as e:
                    logger.error(f"Failed to add user {member.get('username', member['id'])}: {e}")
                    failed_count += 1
                    continue
            
            logger.info(f"Member addition complete. Added: {added_count}, Failed: {failed_count}")
            return {"added": added_count, "failed": failed_count}
            
        except Exception as e:
            logger.error(f"Failed to add members to group: {e}")
            raise

    async def send_bulk_messages(self, recipients, message, delay=1, target_type="users"):
        """Send bulk messages to a list of users or groups

        Args:
            recipients: List of user IDs/usernames or group IDs/usernames
            message: Message text to send
            delay: Delay between messages in seconds
            target_type: "users" or "groups" to specify recipient type
        """
        if not self.is_authenticated:
            raise Exception("Client not authenticated")

        try:
            target_name = "users" if target_type == "users" else "groups"
            logger.info(f"Starting bulk message send to {len(recipients)} {target_name}")

            sent_count = 0
            failed_count = 0

            for recipient in recipients:
                try:
                    # Get the entity (user or group)
                    if isinstance(recipient, dict):
                        entity = await self.client.get_entity(recipient['id'])
                    else:
                        entity = await self.client.get_entity(recipient)

                    # Send the message
                    await self.client.send_message(entity, message)
                    sent_count += 1

                    entity_name = getattr(entity, 'username', None) or getattr(entity, 'title', None) or str(entity.id)
                    logger.info(f"Message sent to {entity_name}")

                    # Rate limiting
                    await asyncio.sleep(delay)

                except FloodWaitError as e:
                    logger.warning(f"Rate limited, waiting {e.seconds} seconds")
                    await asyncio.sleep(e.seconds)
                    continue
                except UserPrivacyRestrictedError:
                    logger.warning(f"User has privacy restrictions, skipping")
                    failed_count += 1
                except Exception as e:
                    logger.error(f"Failed to send message to recipient: {e}")
                    failed_count += 1
                    continue

            logger.info(f"Bulk messaging complete. Sent: {sent_count}, Failed: {failed_count}")
            return {"sent": sent_count, "failed": failed_count}

        except Exception as e:
            logger.error(f"Failed to send bulk messages: {e}")
            raise

    async def send_bulk_messages_to_groups(self, group_identifiers, message, delay=2):
        """Send bulk messages to multiple groups

        Args:
            group_identifiers: List of group IDs, usernames, or invite links
            message: Message text to send
            delay: Delay between messages in seconds (default 2 for groups)
        """
        if not self.is_authenticated:
            raise Exception("Client not authenticated")

        try:
            logger.info(f"Starting bulk message send to {len(group_identifiers)} groups")

            sent_count = 0
            failed_count = 0

            for group_identifier in group_identifiers:
                try:
                    # Get the group entity
                    if isinstance(group_identifier, dict):
                        group = await self.client.get_entity(group_identifier['id'])
                    else:
                        group = await self.client.get_entity(group_identifier)

                    # Check if we can send messages to this group
                    try:
                        # Send the message
                        await self.client.send_message(group, message)
                        sent_count += 1

                        group_name = getattr(group, 'title', None) or getattr(group, 'username', None) or str(group.id)
                        logger.info(f"Message sent to group: {group_name}")

                    except ChatWriteForbiddenError:
                        logger.warning(f"Cannot send messages to group {group.id} (write forbidden)")
                        failed_count += 1
                    except ChatAdminRequiredError:
                        logger.warning(f"Admin privileges required to send to group {group.id}")
                        failed_count += 1

                    # Rate limiting (longer for groups)
                    await asyncio.sleep(delay)

                except FloodWaitError as e:
                    logger.warning(f"Rate limited, waiting {e.seconds} seconds")
                    await asyncio.sleep(e.seconds)
                    continue
                except Exception as e:
                    logger.error(f"Failed to send message to group {group_identifier}: {e}")
                    failed_count += 1
                    continue

            logger.info(f"Group bulk messaging complete. Sent: {sent_count}, Failed: {failed_count}")
            return {"sent": sent_count, "failed": failed_count}

        except Exception as e:
            logger.error(f"Failed to send bulk messages to groups: {e}")
            raise

    async def get_my_groups(self):
        """Get list of groups the user is admin of"""
        if not self.is_authenticated:
            raise Exception("Client not authenticated")

        try:
            dialogs = await self.client.get_dialogs()
            my_groups = []

            for dialog in dialogs:
                if dialog.is_group or dialog.is_channel:
                    entity = dialog.entity
                    # Check if user has admin rights
                    try:
                        permissions = await self.client.get_permissions(entity, 'me')
                        if permissions.is_admin or permissions.is_creator:
                            group_info = {
                                'id': entity.id,
                                'title': entity.title,
                                'username': getattr(entity, 'username', None),
                                'type': 'channel' if dialog.is_channel else 'group',
                                'members_count': getattr(entity, 'participants_count', 0)
                            }
                            my_groups.append(group_info)
                    except:
                        continue

            logger.info(f"Found {len(my_groups)} groups where user has admin rights")
            return my_groups

        except Exception as e:
            logger.error(f"Failed to get user groups: {e}")
            raise

    async def _extract_from_message_history(self, group, limit=None):
        """Extract members from message history - may work for non-admins"""
        members = []
        seen_users = set()

        try:
            logger.info("Scanning message history for active members...")
            message_count = 0
            max_messages = min(500, limit * 10 if limit else 500)  # Reasonable limit to avoid timeouts

            # Get recent messages from the group
            async for message in self.client.iter_messages(group, limit=max_messages):
                message_count += 1

                if message.sender and hasattr(message.sender, 'id'):
                    # Check if it's a user (not a channel or bot)
                    sender = message.sender

                    # Debug: Log sender type for first few messages
                    if message_count <= 10:
                        logger.info(f"Message {message_count}: Sender type: {type(sender).__name__}, ID: {sender.id}")

                    # Only process if it's actually a User, not a Channel
                    if isinstance(sender, User):
                        is_bot = getattr(sender, 'bot', False)
                        is_deleted = getattr(sender, 'deleted', False)

                        if not is_bot and not is_deleted:
                            user_id = sender.id

                            if user_id not in seen_users:
                                seen_users.add(user_id)

                                member_info = {
                                    'id': sender.id,
                                    'username': getattr(sender, 'username', None),
                                    'first_name': getattr(sender, 'first_name', None),
                                    'last_name': getattr(sender, 'last_name', None),
                                    'phone': getattr(sender, 'phone', None),
                                    'access_hash': getattr(sender, 'access_hash', None)
                                }
                                members.append(member_info)
                                logger.info(f"Found valid user: {member_info['username'] or member_info['first_name']} (ID: {member_info['id']})")

                                if limit and len(members) >= limit:
                                    break

                # Rate limiting and progress logging
                if message_count % 100 == 0:
                    await asyncio.sleep(1)
                    logger.info(f"Scanned {message_count} messages, found {len(members)} unique members")

                # Add some delay every 50 messages to avoid timeout
                if message_count % 50 == 0:
                    await asyncio.sleep(0.5)

            logger.info(f"Message history scan complete: {len(members)} members from {message_count} messages")
            return members

        except Exception as e:
            logger.error(f"Error in message history method: {e}")
            raise

    async def _extract_from_recent_activity(self, group, limit=None):
        """Extract members from recent activity using different approaches"""
        members = []
        seen_users = set()

        try:
            # Try to get group info and recent activity
            full_group = await self.client.get_entity(group)

            # Method 1: Try to get online members (if accessible)
            try:
                logger.info("Attempting to get online members...")
                # This might work for some groups
                async for user in self.client.iter_participants(group, filter=None, limit=limit or 100):
                    if not getattr(user, 'bot', False) and not getattr(user, 'deleted', False) and user.id not in seen_users:
                        seen_users.add(user.id)
                        member_info = {
                            'id': user.id,
                            'username': user.username,
                            'first_name': user.first_name,
                            'last_name': user.last_name,
                            'phone': getattr(user, 'phone', None),
                            'access_hash': user.access_hash
                        }
                        members.append(member_info)

                        if limit and len(members) >= limit:
                            break

                    # Rate limiting
                    await asyncio.sleep(0.5)

                if members:
                    return members

            except Exception as e:
                logger.warning(f"Online members approach failed: {e}")

            # Method 2: Try to extract from forwarded messages or replies
            try:
                logger.info("Scanning for forwarded messages and replies...")
                scan_count = 0
                async for message in self.client.iter_messages(group, limit=200):  # Reduced limit
                    scan_count += 1
                    # Check original sender of forwarded messages
                    if message.forward and message.forward.sender:
                        sender = message.forward.sender
                        if isinstance(sender, User) and not getattr(sender, 'bot', False) and not getattr(sender, 'deleted', False) and sender.id not in seen_users:
                            seen_users.add(sender.id)
                            member_info = {
                                'id': sender.id,
                                'username': getattr(sender, 'username', None),
                                'first_name': getattr(sender, 'first_name', None),
                                'last_name': getattr(sender, 'last_name', None),
                                'phone': getattr(sender, 'phone', None),
                                'access_hash': getattr(sender, 'access_hash', None)
                            }
                            members.append(member_info)

                    # Check reply senders
                    if message.reply_to and message.sender:
                        sender = message.sender
                        if isinstance(sender, User) and not getattr(sender, 'bot', False) and not getattr(sender, 'deleted', False) and sender.id not in seen_users:
                            seen_users.add(sender.id)
                            member_info = {
                                'id': sender.id,
                                'username': getattr(sender, 'username', None),
                                'first_name': getattr(sender, 'first_name', None),
                                'last_name': getattr(sender, 'last_name', None),
                                'phone': getattr(sender, 'phone', None),
                                'access_hash': getattr(sender, 'access_hash', None)
                            }
                            members.append(member_info)

                    if limit and len(members) >= limit:
                        break

                    # Rate limiting
                    if scan_count % 50 == 0:
                        await asyncio.sleep(0.5)
                        logger.info(f"Scanned {scan_count} forwarded/reply messages, found {len(members)} members")

            except Exception as e:
                logger.warning(f"Forwarded messages approach failed: {e}")

            return members

        except Exception as e:
            logger.error(f"Error in recent activity method: {e}")
            raise

    async def _extract_admins(self, group, limit=None):
        """Extract admin users from the group - this often works even without admin privileges"""
        members = []
        seen_users = set()

        try:
            logger.info("Attempting to extract group admins...")

            # Method 1: Try to get admins using ChannelParticipantsAdmins filter
            try:

                admins = await self.client(GetParticipantsRequest(
                    group,
                    ChannelParticipantsAdmins(),
                    0,
                    100,  # Usually not many admins
                    hash=0
                ))

                logger.info(f"Found {len(admins.users)} admin users")

                for user in admins.users:
                    if isinstance(user, User) and not getattr(user, 'bot', False) and not getattr(user, 'deleted', False):
                        if user.id not in seen_users:
                            seen_users.add(user.id)

                            member_info = {
                                'id': user.id,
                                'username': getattr(user, 'username', None),
                                'first_name': getattr(user, 'first_name', None),
                                'last_name': getattr(user, 'last_name', None),
                                'phone': getattr(user, 'phone', None),
                                'access_hash': getattr(user, 'access_hash', None),
                                'is_admin': True  # Mark as admin
                            }
                            members.append(member_info)
                            logger.info(f"Found admin: {member_info['username'] or member_info['first_name']} (ID: {member_info['id']})")

                            if limit and len(members) >= limit:
                                break

                return members

            except Exception as e:
                logger.warning(f"Direct admin extraction failed: {e}")

            # Method 2: Look for admin messages (messages with admin signatures)
            try:
                logger.info("Scanning for admin signatures in messages...")
                message_count = 0

                async for message in self.client.iter_messages(group, limit=200):
                    message_count += 1

                    # Check if message has admin signature or is from an admin
                    if message.sender and isinstance(message.sender, User):
                        # Look for admin indicators in message
                        if (hasattr(message, 'post_author') and message.post_author) or \
                           (hasattr(message, 'edit_date') and message.edit_date):

                            sender = message.sender
                            if not getattr(sender, 'bot', False) and not getattr(sender, 'deleted', False):
                                if sender.id not in seen_users:
                                    seen_users.add(sender.id)

                                    member_info = {
                                        'id': sender.id,
                                        'username': getattr(sender, 'username', None),
                                        'first_name': getattr(sender, 'first_name', None),
                                        'last_name': getattr(sender, 'last_name', None),
                                        'phone': getattr(sender, 'phone', None),
                                        'access_hash': getattr(sender, 'access_hash', None),
                                        'is_admin': True,  # Likely admin
                                        'source': 'admin_message'
                                    }
                                    members.append(member_info)
                                    logger.info(f"Found potential admin from messages: {member_info['username'] or member_info['first_name']}")

                    if message_count % 50 == 0:
                        await asyncio.sleep(0.5)

                    if limit and len(members) >= limit:
                        break

            except Exception as e:
                logger.warning(f"Admin message scanning failed: {e}")

            logger.info(f"Admin extraction complete: {len(members)} admins found")
            return members

        except Exception as e:
            logger.error(f"Error in admin extraction: {e}")
            raise

    async def _extract_from_reactions(self, group, limit=None):
        """Try to extract members from message reactions (for channels/groups with reactions)"""
        members = []
        seen_users = set()

        try:
            logger.info("Scanning message reactions for users...")
            message_count = 0

            # Get recent messages and check for reactions
            async for message in self.client.iter_messages(group, limit=100):
                message_count += 1

                try:
                    # Check if message has reactions
                    if hasattr(message, 'reactions') and message.reactions:
                        reaction_count = sum(r.count for r in message.reactions.results) if message.reactions.results else 0
                        logger.info(f"Message {message.id} has {reaction_count} total reactions")

                        # Check if we can see the reaction list
                        if hasattr(message.reactions, 'can_see_list') and message.reactions.can_see_list:
                            # Try to get users who reacted using GetMessageReactionsListRequest
                            try:
                                from telethon.tl.functions.messages import GetMessageReactionsListRequest

                                # Try to get reaction list (may require permissions)
                                reaction_list = await self.client(GetMessageReactionsListRequest(
                                    peer=group,
                                    id=message.id,
                                    limit=50
                                ))

                                if hasattr(reaction_list, 'users'):
                                    for user in reaction_list.users:
                                        if isinstance(user, User) and not getattr(user, 'bot', False) and not getattr(user, 'deleted', False):
                                            if user.id not in seen_users:
                                                seen_users.add(user.id)

                                                member_info = {
                                                    'id': user.id,
                                                    'username': getattr(user, 'username', None),
                                                    'first_name': getattr(user, 'first_name', None),
                                                    'last_name': getattr(user, 'last_name', None),
                                                    'phone': getattr(user, 'phone', None),
                                                    'access_hash': getattr(user, 'access_hash', None),
                                                    'source': 'reaction'
                                                }
                                                members.append(member_info)
                                                logger.info(f"Found user from reactions: {member_info['username'] or member_info['first_name']}")

                                                if limit and len(members) >= limit:
                                                    break

                            except Exception as e:
                                logger.debug(f"Could not get reaction users for message {message.id}: {e}")
                        else:
                            logger.debug(f"Cannot see reaction list for message {message.id} (can_see_list=False)")

                            # Alternative: Try to get recent reactors if available
                            if hasattr(message.reactions, 'recent_reactions') and message.reactions.recent_reactions:
                                logger.info(f"Found {len(message.reactions.recent_reactions)} recent reactions")
                                for recent_reaction in message.reactions.recent_reactions:
                                    try:
                                        # Extract user ID from recent reaction
                                        user_id = None
                                        if hasattr(recent_reaction, 'peer_id'):
                                            if hasattr(recent_reaction.peer_id, 'user_id'):
                                                user_id = recent_reaction.peer_id.user_id
                                            else:
                                                user_id = recent_reaction.peer_id
                                        elif hasattr(recent_reaction, 'user_id'):
                                            user_id = recent_reaction.user_id

                                        if user_id:
                                            logger.info(f"Attempting to get user from recent reaction ID: {user_id}")
                                            user = await self.client.get_entity(user_id)

                                            if isinstance(user, User) and not getattr(user, 'bot', False) and not getattr(user, 'deleted', False):
                                                if user.id not in seen_users:
                                                    seen_users.add(user.id)

                                                    member_info = {
                                                        'id': user.id,
                                                        'username': getattr(user, 'username', None),
                                                        'first_name': getattr(user, 'first_name', None),
                                                        'last_name': getattr(user, 'last_name', None),
                                                        'phone': getattr(user, 'phone', None),
                                                        'access_hash': getattr(user, 'access_hash', None),
                                                        'source': 'recent_reaction'
                                                    }
                                                    members.append(member_info)
                                                    logger.info(f"✅ SUCCESS! Found user from recent reactions: {member_info['username'] or member_info['first_name']} (ID: {member_info['id']})")

                                                    if limit and len(members) >= limit:
                                                        break
                                    except Exception as e:
                                        logger.warning(f"Could not get user from recent reaction: {e}")
                                        logger.debug(f"Recent reaction object: {recent_reaction}")

                            # Alternative: Check top reactors if available
                            if hasattr(message.reactions, 'top_reactors') and message.reactions.top_reactors:
                                try:
                                    logger.info(f"Found {len(message.reactions.top_reactors)} top reactors")
                                    logger.info(f"Top reactors object: {message.reactions.top_reactors}")

                                    for i, top_reactor in enumerate(message.reactions.top_reactors):
                                        try:
                                            logger.info(f"Top reactor {i}: {top_reactor} (type: {type(top_reactor)})")

                                            # Let's examine all attributes of the top_reactor object
                                            logger.info(f"Top reactor attributes: {dir(top_reactor)}")

                                            # Try to extract any user-related information
                                            for attr in ['user_id', 'id', 'peer_id', 'peer', 'user']:
                                                if hasattr(top_reactor, attr):
                                                    logger.info(f"Top reactor has {attr}: {getattr(top_reactor, attr)}")

                                            # Try to get user ID from top reactor
                                            user_id = None
                                            if hasattr(top_reactor, 'user_id'):
                                                user_id = top_reactor.user_id
                                            elif hasattr(top_reactor, 'id'):
                                                user_id = top_reactor.id
                                            elif hasattr(top_reactor, 'peer_id'):
                                                if hasattr(top_reactor.peer_id, 'user_id'):
                                                    user_id = top_reactor.peer_id.user_id
                                                else:
                                                    user_id = top_reactor.peer_id
                                            else:
                                                user_id = top_reactor

                                            if user_id:
                                                logger.info(f"Attempting to get user from top reactor ID: {user_id}")
                                                user = await self.client.get_entity(user_id)

                                                if isinstance(user, User) and not getattr(user, 'bot', False) and not getattr(user, 'deleted', False):
                                                    if user.id not in seen_users:
                                                        seen_users.add(user.id)

                                                        member_info = {
                                                            'id': user.id,
                                                            'username': getattr(user, 'username', None),
                                                            'first_name': getattr(user, 'first_name', None),
                                                            'last_name': getattr(user, 'last_name', None),
                                                            'phone': getattr(user, 'phone', None),
                                                            'access_hash': getattr(user, 'access_hash', None),
                                                            'source': 'top_reactor'
                                                        }
                                                        members.append(member_info)
                                                        logger.info(f"✅ SUCCESS! Found user from top reactors: {member_info['username'] or member_info['first_name']} (ID: {member_info['id']})")

                                                        if limit and len(members) >= limit:
                                                            break
                                            else:
                                                logger.warning(f"Could not extract user ID from top reactor: {top_reactor}")

                                        except Exception as e:
                                            logger.warning(f"Error processing top reactor {i}: {e}")
                                            continue

                                        # Only process first few to avoid spam
                                        if i >= 2:
                                            break

                                except Exception as e:
                                    logger.warning(f"Error processing top reactors: {e}")

                except Exception as e:
                    logger.debug(f"Could not process reactions for message {message.id}: {e}")
                    continue

                if message_count >= 100:
                    break

                await asyncio.sleep(0.2)

                if limit and len(members) >= limit:
                    break

            logger.info(f"Reaction scan complete: {len(members)} members from {message_count} messages")
            return members

        except Exception as e:
            logger.error(f"Error in reaction method: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise

    async def _extract_from_comments(self, group, limit=None):
        """Extract users from channel comments (for channels with comments enabled)"""
        members = []
        seen_users = set()

        try:
            logger.info("Scanning channel comments for users...")
            message_count = 0

            # Get recent messages and check for comments
            async for message in self.client.iter_messages(group, limit=50):
                message_count += 1

                try:
                    # Check if message has comments/discussion
                    if hasattr(message, 'replies') and message.replies and message.replies.replies > 0:
                        logger.info(f"Message {message.id} has {message.replies.replies} replies")

                        try:
                            # Method 1: Try GetDiscussionMessageRequest
                            from telethon.tl.functions.messages import GetDiscussionMessageRequest

                            discussion = await self.client(GetDiscussionMessageRequest(
                                peer=group,
                                msg_id=message.id
                            ))

                            # Get comments from the discussion
                            if hasattr(discussion, 'messages'):
                                for comment_msg in discussion.messages:
                                    if comment_msg.sender and isinstance(comment_msg.sender, User):
                                        sender = comment_msg.sender
                                        if not getattr(sender, 'bot', False) and not getattr(sender, 'deleted', False):
                                            if sender.id not in seen_users:
                                                seen_users.add(sender.id)

                                                member_info = {
                                                    'id': sender.id,
                                                    'username': getattr(sender, 'username', None),
                                                    'first_name': getattr(sender, 'first_name', None),
                                                    'last_name': getattr(sender, 'last_name', None),
                                                    'phone': getattr(sender, 'phone', None),
                                                    'access_hash': getattr(sender, 'access_hash', None),
                                                    'source': 'comment'
                                                }
                                                members.append(member_info)
                                                logger.info(f"Found user from comments: {member_info['username'] or member_info['first_name']}")

                                                if limit and len(members) >= limit:
                                                    break

                        except Exception as e:
                            logger.debug(f"GetDiscussionMessageRequest failed for message {message.id}: {e}")

                            # Method 2: Try alternative approach - get replies directly
                            try:
                                if hasattr(message.replies, 'channel_id'):
                                    # This message has a linked discussion channel
                                    discussion_channel = message.replies.channel_id
                                    logger.info(f"Found discussion channel: {discussion_channel}")

                                    # Try to get messages from discussion channel
                                    discussion_entity = await self.client.get_entity(discussion_channel)

                                    async for reply_msg in self.client.iter_messages(discussion_entity, limit=20):
                                        if reply_msg.sender and isinstance(reply_msg.sender, User):
                                            sender = reply_msg.sender
                                            if not getattr(sender, 'bot', False) and not getattr(sender, 'deleted', False):
                                                if sender.id not in seen_users:
                                                    seen_users.add(sender.id)

                                                    member_info = {
                                                        'id': sender.id,
                                                        'username': getattr(sender, 'username', None),
                                                        'first_name': getattr(sender, 'first_name', None),
                                                        'last_name': getattr(sender, 'last_name', None),
                                                        'phone': getattr(sender, 'phone', None),
                                                        'access_hash': getattr(sender, 'access_hash', None),
                                                        'source': 'discussion_channel'
                                                    }
                                                    members.append(member_info)
                                                    logger.info(f"Found user from discussion channel: {member_info['username'] or member_info['first_name']}")

                                                    if limit and len(members) >= limit:
                                                        break
                            except Exception as e2:
                                logger.debug(f"Discussion channel approach failed for message {message.id}: {e2}")

                except Exception as e:
                    logger.debug(f"Could not process comments for message {message.id}: {e}")
                    continue

                await asyncio.sleep(0.3)

                if limit and len(members) >= limit:
                    break

            logger.info(f"Comment scan complete: {len(members)} members from {message_count} messages")
            return members

        except Exception as e:
            logger.error(f"Error in comment method: {e}")
            raise

    async def _extract_from_channel_exploration(self, group, limit=None):
        """Try alternative methods to extract users from highly engaged channels"""
        members = []
        seen_users = set()

        try:
            logger.info("Exploring channel for user extraction opportunities...")

            # Method 1: Try to get channel info and linked chat
            try:
                full_channel = await self.client.get_entity(group)
                logger.info(f"Channel info: {type(full_channel).__name__}")

                # Check if channel has linked discussion group
                if hasattr(full_channel, 'linked_chat_id') and full_channel.linked_chat_id:
                    logger.info(f"Found linked discussion group: {full_channel.linked_chat_id}")

                    try:
                        discussion_group = await self.client.get_entity(full_channel.linked_chat_id)
                        logger.info(f"Attempting to extract from linked discussion group...")

                        # Try to get members from the discussion group
                        discussion_members = await self._extract_from_message_history(discussion_group, limit)
                        if discussion_members:
                            logger.info(f"Found {len(discussion_members)} members from linked discussion group")
                            return discussion_members

                    except Exception as e:
                        logger.debug(f"Could not access linked discussion group: {e}")

            except Exception as e:
                logger.debug(f"Could not get channel info: {e}")

            # Method 2: Try to find users in message forwards/mentions
            try:
                logger.info("Scanning for user mentions and forwards...")
                message_count = 0

                async for message in self.client.iter_messages(group, limit=200):
                    message_count += 1

                    # Check for user mentions in message text
                    if message.text:
                        import re
                        mentions = re.findall(r'@(\w+)', message.text)
                        for mention in mentions:
                            try:
                                user = await self.client.get_entity(f"@{mention}")
                                if isinstance(user, User) and not getattr(user, 'bot', False):
                                    if user.id not in seen_users:
                                        seen_users.add(user.id)

                                        member_info = {
                                            'id': user.id,
                                            'username': getattr(user, 'username', None),
                                            'first_name': getattr(user, 'first_name', None),
                                            'last_name': getattr(user, 'last_name', None),
                                            'phone': getattr(user, 'phone', None),
                                            'access_hash': getattr(user, 'access_hash', None),
                                            'source': 'mention'
                                        }
                                        members.append(member_info)
                                        logger.info(f"Found user from mention: @{mention}")

                                        if limit and len(members) >= limit:
                                            break
                            except Exception as e:
                                logger.debug(f"Could not resolve mention @{mention}: {e}")

                    # Check for forwarded messages
                    if hasattr(message, 'forward') and message.forward:
                        try:
                            if hasattr(message.forward, 'from_id') and message.forward.from_id:
                                user = await self.client.get_entity(message.forward.from_id)
                                if isinstance(user, User) and not getattr(user, 'bot', False):
                                    if user.id not in seen_users:
                                        seen_users.add(user.id)

                                        member_info = {
                                            'id': user.id,
                                            'username': getattr(user, 'username', None),
                                            'first_name': getattr(user, 'first_name', None),
                                            'last_name': getattr(user, 'last_name', None),
                                            'phone': getattr(user, 'phone', None),
                                            'access_hash': getattr(user, 'access_hash', None),
                                            'source': 'forward'
                                        }
                                        members.append(member_info)
                                        logger.info(f"Found user from forward: {member_info['username'] or member_info['first_name']}")

                                        if limit and len(members) >= limit:
                                            break
                        except Exception as e:
                            logger.debug(f"Could not process forward: {e}")

                    if message_count % 50 == 0:
                        await asyncio.sleep(0.5)
                        logger.info(f"Scanned {message_count} messages for mentions/forwards, found {len(members)} users")

                    if limit and len(members) >= limit:
                        break

            except Exception as e:
                logger.debug(f"Mention/forward scanning failed: {e}")

            logger.info(f"Channel exploration complete: {len(members)} members found")
            return members

        except Exception as e:
            logger.error(f"Error in channel exploration: {e}")
            raise

    async def disconnect(self):
        """Disconnect the client"""
        if self.client:
            await self.client.disconnect()
            logger.info("Telegram client disconnected")
