#!/usr/bin/env python3
"""
Simple script to test Telegram API credentials
"""

import asyncio
from telethon import TelegramClient
from config import Config

async def test_credentials():
    print("Testing Telegram API credentials...")
    print(f"API ID: {Config.API_ID}")
    print(f"API Hash: {Config.API_HASH}")
    print(f"Phone: {Config.PHONE_NUMBER}")
    print("-" * 50)
    
    try:
        client = TelegramClient('test_session', Config.API_ID, Config.API_HASH)
        print("Attempting to connect...")
        
        await client.connect()
        print("✅ Connection successful!")
        
        print("Attempting to send code request...")
        await client.send_code_request(Config.PHONE_NUMBER)
        print("✅ Code request sent successfully!")
        print("📱 Check your Telegram app for the verification code")
        
        await client.disconnect()
        print("✅ Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nPossible issues:")
        print("1. API ID or API Hash is incorrect")
        print("2. Phone number format is wrong")
        print("3. Network connectivity issues")

if __name__ == "__main__":
    asyncio.run(test_credentials())
