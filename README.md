# Telegram Group Manager

A powerful Python application for managing Telegram groups with member transfer and bulk messaging capabilities. Features a tablet-friendly web interface for easy operation.

## Features

- **Group Member Extraction**: Extract member lists from any Telegram group
- **Member Transfer**: Transfer members from one group to another
- **Bulk Messaging**: Send messages to multiple users at once
- **Tablet-Friendly Interface**: Responsive web UI optimized for tablets
- **Rate Limiting**: Built-in protection against Telegram API limits
- **Comprehensive Logging**: Detailed logs for all operations

## Prerequisites

1. **Python 3.7+** installed on your system
2. **Telegram API credentials** (API ID and API Hash)
3. **Phone number** registered with Telegram

## Setup Instructions

### 1. Get Telegram API Credentials

1. Go to [https://my.telegram.org/apps](https://my.telegram.org/apps)
2. Log in with your phone number
3. Create a new application
4. Note down your `API ID` and `API Hash`

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configure Environment

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` file with your credentials:
   ```
   API_ID=your_api_id_here
   API_HASH=your_api_hash_here
   PHONE_NUMBER=+1234567890
   ```

### 4. Run the Application

```bash
python app.py
```

The web interface will be available at `http://localhost:5000`

## Usage

### 1. Connect to Telegram
- Open the web interface
- Click "Connect" button
- Enter the verification code sent to your phone

### 2. Extract Group Members
- Enter the source group username (e.g., `@groupname`) or ID
- Optionally set a limit for number of members
- Click "Extract Members"

### 3. Transfer Members
- After extracting members, enter the target group
- Set delay between additions (recommended: 2-5 seconds)
- Click "Transfer Members"

### 4. Send Bulk Messages
- Write your message in the text area
- Choose recipients (extracted members or custom)
- Set delay between messages (recommended: 1-2 seconds)
- Click "Send Bulk Messages"

## Important Notes

### Rate Limiting
- The application includes built-in rate limiting to prevent Telegram API restrictions
- Recommended delays:
  - Member transfers: 2-5 seconds between additions
  - Bulk messages: 1-2 seconds between messages

### Permissions Required
- You must be an admin of the target group to add members
- Some users may have privacy settings that prevent being added to groups
- The application will skip users that cannot be added and report the results

### Legal and Ethical Use
- Only use this tool for legitimate purposes
- Respect user privacy and Telegram's Terms of Service
- Do not use for spam or unsolicited messaging
- Always obtain proper consent before adding users to groups

## Troubleshooting

### Common Issues

1. **"Client not authenticated"**
   - Make sure you clicked "Connect" and completed phone verification

2. **"Failed to extract members"**
   - Check if the group username/ID is correct
   - Ensure you have access to view the group members

3. **"Failed to add members"**
   - Verify you're an admin of the target group
   - Some users may have privacy restrictions

4. **Rate limiting errors**
   - Increase the delay settings
   - Wait before retrying operations

### Logs
Check the `logs/` directory for detailed error information and operation history.

## File Structure

```
Py-Telegram/
├── app.py                 # Main Flask application
├── telegram_client.py     # Telegram API client
├── config.py             # Configuration management
├── logger.py             # Logging setup
├── requirements.txt      # Python dependencies
├── .env.example         # Environment template
├── templates/
│   └── index.html       # Web interface
├── static/
│   └── app.js          # Frontend JavaScript
└── logs/               # Application logs
```

## Security

- Never share your API credentials
- Keep your `.env` file secure and never commit it to version control
- Use strong session passwords if prompted
- Regularly review application logs for suspicious activity

## Support

For issues and questions:
1. Check the logs in the `logs/` directory
2. Verify your configuration in the `.env` file
3. Ensure you have the latest version of all dependencies

## License

This project is for educational and legitimate business purposes only. Users are responsible for complying with Telegram's Terms of Service and applicable laws.
