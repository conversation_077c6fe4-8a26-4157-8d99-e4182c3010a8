from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
import asyncio
import json
import os
from telegram_client import TelegramAutomation
from config import Config
from logger import logger
import threading
import time

app = Flask(__name__)
app.secret_key = os.urandom(24)
CORS(app)

# Global telegram client instance
telegram_client = TelegramAutomation()

# Global event loop for telegram operations
telegram_loop = None
telegram_thread = None

def run_telegram_loop():
    """Run the telegram event loop in a separate thread"""
    global telegram_loop
    telegram_loop = asyncio.new_event_loop()
    asyncio.set_event_loop(telegram_loop)
    telegram_loop.run_forever()

def run_in_telegram_loop(coro):
    """Run a coroutine in the telegram event loop"""
    global telegram_loop
    if telegram_loop is None:
        return None
    future = asyncio.run_coroutine_threadsafe(coro, telegram_loop)
    return future.result(timeout=30)

# Start the telegram event loop thread
telegram_thread = threading.Thread(target=run_telegram_loop, daemon=True)
telegram_thread.start()
time.sleep(1)  # Give the thread time to start

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/status')
def status():
    return jsonify({
        'authenticated': telegram_client.is_authenticated,
        'config_valid': check_config()
    })

@app.route('/api/initialize', methods=['POST'])
def initialize_client():
    try:
        result = run_in_telegram_loop(telegram_client.initialize())

        if result:
            if telegram_client.is_authenticated:
                return jsonify({'success': True, 'message': 'Already authenticated'})
            else:
                return jsonify({'success': True, 'message': 'Code sent to your Telegram app', 'needs_code': True})
        else:
            return jsonify({'success': False, 'message': 'Failed to initialize client'})
    except Exception as e:
        logger.error(f"Initialization error: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/verify-code', methods=['POST'])
def verify_code():
    try:
        data = request.json
        code = data.get('code')

        if not code:
            return jsonify({'success': False, 'message': 'Verification code is required'})

        result = run_in_telegram_loop(telegram_client.verify_code(code))

        if result:
            return jsonify({'success': True, 'message': 'Successfully authenticated'})
        else:
            return jsonify({'success': False, 'message': 'Invalid verification code'})
    except Exception as e:
        logger.error(f"Code verification error: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/groups')
def get_groups():
    try:
        if not telegram_client.is_authenticated:
            return jsonify({'success': False, 'message': 'Client not authenticated'})

        groups = run_in_telegram_loop(telegram_client.get_my_groups())

        return jsonify({'success': True, 'groups': groups})
    except Exception as e:
        logger.error(f"Error getting groups: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/extract-members', methods=['POST'])
def extract_members():
    try:
        data = request.json
        group_identifier = data.get('group_identifier')
        limit = data.get('limit')

        if not group_identifier:
            return jsonify({'success': False, 'message': 'Group identifier is required'})

        if not telegram_client.is_authenticated:
            return jsonify({'success': False, 'message': 'Client not authenticated'})

        members = run_in_telegram_loop(
            telegram_client.get_group_members(group_identifier, limit)
        )

        # Store members in session for later use
        session['extracted_members'] = members

        return jsonify({
            'success': True,
            'members': members,
            'count': len(members)
        })
    except Exception as e:
        logger.error(f"Error extracting members: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/transfer-members', methods=['POST'])
def transfer_members():
    try:
        data = request.json
        target_group = data.get('target_group')
        delay = data.get('delay', 2)

        if not target_group:
            return jsonify({'success': False, 'message': 'Target group is required'})

        members = session.get('extracted_members', [])
        if not members:
            return jsonify({'success': False, 'message': 'No members to transfer. Extract members first.'})

        if not telegram_client.is_authenticated:
            return jsonify({'success': False, 'message': 'Client not authenticated'})

        result = run_in_telegram_loop(
            telegram_client.add_members_to_group(target_group, members, delay)
        )

        return jsonify({
            'success': True,
            'result': result,
            'message': f"Transfer complete. Added: {result['added']}, Failed: {result['failed']}"
        })
    except Exception as e:
        logger.error(f"Error transferring members: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/send-bulk-messages', methods=['POST'])
def send_bulk_messages():
    try:
        data = request.json
        message = data.get('message')
        recipients_type = data.get('recipients_type', 'extracted')  # 'extracted' or 'custom'
        custom_recipients = data.get('custom_recipients', [])
        delay = data.get('delay', 1)

        if not message:
            return jsonify({'success': False, 'message': 'Message is required'})

        if recipients_type == 'extracted':
            recipients = session.get('extracted_members', [])
            if not recipients:
                return jsonify({'success': False, 'message': 'No recipients found. Extract members first.'})
        else:
            recipients = custom_recipients
            if not recipients:
                return jsonify({'success': False, 'message': 'No custom recipients provided'})

        if not telegram_client.is_authenticated:
            return jsonify({'success': False, 'message': 'Client not authenticated'})

        result = run_in_telegram_loop(
            telegram_client.send_bulk_messages(recipients, message, delay)
        )

        return jsonify({
            'success': True,
            'result': result,
            'message': f"Messaging complete. Sent: {result['sent']}, Failed: {result['failed']}"
        })
    except Exception as e:
        logger.error(f"Error sending bulk messages: {e}")
        return jsonify({'success': False, 'message': str(e)})

def check_config():
    try:
        Config.validate()
        return True
    except:
        return False

if __name__ == '__main__':
    app.run(
        host=Config.FLASK_HOST,
        port=Config.FLASK_PORT,
        debug=Config.FLASK_DEBUG
    )
