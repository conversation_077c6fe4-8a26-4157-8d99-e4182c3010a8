class TelegramManager {
    constructor() {
        this.isConnected = false;
        this.extractedMembers = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkStatus();
    }

    bindEvents() {
        document.getElementById('connect-btn').addEventListener('click', () => this.connect());
        document.getElementById('extract-btn').addEventListener('click', () => this.extractMembers());
        document.getElementById('transfer-btn').addEventListener('click', () => this.transferMembers());
        document.getElementById('send-bulk-btn').addEventListener('click', () => this.sendBulkMessages());
    }

    async checkStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            this.updateStatus(data.authenticated);
        } catch (error) {
            console.error('Error checking status:', error);
            this.showError('Failed to check connection status');
        }
    }

    async connect() {
        const connectBtn = document.getElementById('connect-btn');
        const originalText = connectBtn.innerHTML;

        connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Connecting...';
        connectBtn.disabled = true;

        try {
            const response = await fetch('/api/initialize', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (data.success) {
                if (data.needs_code) {
                    // Need verification code
                    this.showSuccess('Code sent to your Telegram app!');
                    this.promptForCode();
                } else {
                    // Already authenticated
                    this.updateStatus(true);
                    this.showSuccess('Connected successfully!');
                }
            } else {
                this.showError(data.message || 'Failed to connect');
            }
        } catch (error) {
            console.error('Connection error:', error);
            this.showError('Connection failed. Please check your configuration.');
        } finally {
            connectBtn.innerHTML = originalText;
            connectBtn.disabled = false;
        }
    }

    promptForCode() {
        const code = prompt('Enter the verification code from your Telegram app:');
        if (code) {
            this.verifyCode(code);
        }
    }

    async verifyCode(code) {
        try {
            const response = await fetch('/api/verify-code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ code: code })
            });

            const data = await response.json();

            if (data.success) {
                this.updateStatus(true);
                this.showSuccess('Successfully authenticated!');
            } else {
                this.showError(data.message || 'Invalid verification code');
                // Ask for code again
                setTimeout(() => this.promptForCode(), 2000);
            }
        } catch (error) {
            console.error('Verification error:', error);
            this.showError('Failed to verify code');
        }
    }

    updateStatus(connected) {
        this.isConnected = connected;
        const indicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        const mainInterface = document.getElementById('main-interface');
        const connectBtn = document.getElementById('connect-btn');

        if (connected) {
            indicator.className = 'status-indicator status-connected';
            statusText.textContent = 'Connected';
            mainInterface.style.display = 'block';
            connectBtn.innerHTML = '<i class="fas fa-check me-2"></i>Connected';
            connectBtn.disabled = true;
        } else {
            indicator.className = 'status-indicator status-disconnected';
            statusText.textContent = 'Disconnected';
            mainInterface.style.display = 'none';
            connectBtn.innerHTML = '<i class="fas fa-plug me-2"></i>Connect';
            connectBtn.disabled = false;
        }
    }

    async extractMembers() {
        const sourceGroup = document.getElementById('source-group').value.trim();
        const memberLimit = document.getElementById('member-limit').value;
        
        if (!sourceGroup) {
            this.showError('Please enter a source group');
            return;
        }

        if (!this.isConnected) {
            this.showError('Please connect first');
            return;
        }

        this.showLoading('extract-loading', true);
        document.getElementById('extract-btn').disabled = true;

        try {
            const response = await fetch('/api/extract-members', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    group_identifier: sourceGroup,
                    limit: memberLimit ? parseInt(memberLimit) : null
                })
            });

            const data = await response.json();

            if (data.success) {
                this.extractedMembers = data.members;
                document.getElementById('member-count').textContent = data.count;
                document.getElementById('extracted-info').style.display = 'block';
                this.showSuccess(`Successfully extracted ${data.count} members!`);
            } else {
                this.showError(data.message || 'Failed to extract members');
            }
        } catch (error) {
            console.error('Extract error:', error);
            this.showError('Failed to extract members');
        } finally {
            this.showLoading('extract-loading', false);
            document.getElementById('extract-btn').disabled = false;
        }
    }

    async transferMembers() {
        const targetGroup = document.getElementById('target-group').value.trim();
        const delay = document.getElementById('transfer-delay').value;

        if (!targetGroup) {
            this.showError('Please enter a target group');
            return;
        }

        if (this.extractedMembers.length === 0) {
            this.showError('No members to transfer. Extract members first.');
            return;
        }

        if (!this.isConnected) {
            this.showError('Please connect first');
            return;
        }

        this.showLoading('transfer-loading', true);
        document.getElementById('transfer-btn').disabled = true;

        try {
            const response = await fetch('/api/transfer-members', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    target_group: targetGroup,
                    delay: parseInt(delay)
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showResults('Member Transfer Results', data.result);
            } else {
                this.showError(data.message || 'Failed to transfer members');
            }
        } catch (error) {
            console.error('Transfer error:', error);
            this.showError('Failed to transfer members');
        } finally {
            this.showLoading('transfer-loading', false);
            document.getElementById('transfer-btn').disabled = false;
        }
    }

    async sendBulkMessages() {
        const message = document.getElementById('bulk-message').value.trim();
        const recipientsType = document.getElementById('recipients-type').value;
        const delay = document.getElementById('message-delay').value;

        if (!message) {
            this.showError('Please enter a message');
            return;
        }

        if (recipientsType === 'extracted' && this.extractedMembers.length === 0) {
            this.showError('No recipients found. Extract members first.');
            return;
        }

        if (!this.isConnected) {
            this.showError('Please connect first');
            return;
        }

        this.showLoading('message-loading', true);
        document.getElementById('send-bulk-btn').disabled = true;

        try {
            const response = await fetch('/api/send-bulk-messages', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    recipients_type: recipientsType,
                    delay: parseInt(delay)
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showResults('Bulk Messaging Results', data.result);
            } else {
                this.showError(data.message || 'Failed to send messages');
            }
        } catch (error) {
            console.error('Messaging error:', error);
            this.showError('Failed to send messages');
        } finally {
            this.showLoading('message-loading', false);
            document.getElementById('send-bulk-btn').disabled = false;
        }
    }

    showLoading(elementId, show) {
        const element = document.getElementById(elementId);
        element.style.display = show ? 'block' : 'none';
    }

    showError(message) {
        this.showAlert(message, 'danger');
    }

    showSuccess(message) {
        this.showAlert(message, 'success');
    }

    showAlert(message, type) {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    showResults(title, results) {
        const modal = new bootstrap.Modal(document.getElementById('resultsModal'));
        document.querySelector('#resultsModal .modal-title').textContent = title;
        
        const content = document.getElementById('results-content');
        content.innerHTML = `
            <div class="row">
                <div class="col-6">
                    <div class="text-center">
                        <h4 class="text-success">${results.added || results.sent || 0}</h4>
                        <p class="text-muted">Successful</p>
                    </div>
                </div>
                <div class="col-6">
                    <div class="text-center">
                        <h4 class="text-danger">${results.failed || 0}</h4>
                        <p class="text-muted">Failed</p>
                    </div>
                </div>
            </div>
        `;
        
        modal.show();
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TelegramManager();
});
