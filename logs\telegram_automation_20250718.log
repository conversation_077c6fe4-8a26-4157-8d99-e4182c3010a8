2025-07-18 04:37:11,491 - telethon.crypto.libssl - INFO - Failed to load SSL library: <class 'OSError'> (no library called "ssl" found)
2025-07-18 04:37:11,493 - telethon.crypto.aes - INFO - cryptg module not installed and libssl not found, falling back to (slower) Python encryption
2025-07-18 04:37:12,537 - root - INFO - All dependencies are installed
2025-07-18 04:37:12,538 - root - INFO - Configuration is valid
2025-07-18 04:37:12,539 - root - INFO - Created directory: sessions
2025-07-18 04:37:12,606 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:37:12,607 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:37:32,853 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:32] "GET / HTTP/1.1" 200 -
2025-07-18 04:37:32,964 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:32] "GET /static/app.js HTTP/1.1" 200 -
2025-07-18 04:37:33,592 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:33] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:37:33,810 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:33] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-18 04:37:36,516 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:37:38,185 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:37:38,602 - root - ERROR - Failed to initialize Telegram client: The api_id/api_hash combination is invalid (caused by SendCodeRequest)
2025-07-18 04:37:38,604 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:38] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:37:46,212 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:37:46,279 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:37:47,003 - root - ERROR - Failed to initialize Telegram client: The api_id/api_hash combination is invalid (caused by SendCodeRequest)
2025-07-18 04:37:47,007 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:47] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:39:50,790 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:50] "GET / HTTP/1.1" 200 -
2025-07-18 04:39:50,952 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:50] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:39:51,135 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:51] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:39:53,828 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:39:53,894 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:39:54,295 - root - ERROR - Failed to initialize Telegram client: The api_id/api_hash combination is invalid (caused by SendCodeRequest)
2025-07-18 04:39:54,297 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:54] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:42:48,179 - telethon.crypto.libssl - INFO - Failed to load SSL library: <class 'OSError'> (no library called "ssl" found)
2025-07-18 04:42:48,181 - telethon.crypto.aes - INFO - cryptg module not installed and libssl not found, falling back to (slower) Python encryption
2025-07-18 04:42:49,077 - root - INFO - All dependencies are installed
2025-07-18 04:42:49,077 - root - INFO - Configuration is valid
2025-07-18 04:42:49,137 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:42:49,138 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:43:24,194 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:24] "GET / HTTP/1.1" 200 -
2025-07-18 04:43:24,257 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:24] "GET /static/app.js HTTP/1.1" 200 -
2025-07-18 04:43:24,330 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:24] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:43:26,081 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:26] "GET / HTTP/1.1" 200 -
2025-07-18 04:43:26,139 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:26] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:43:26,161 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:26] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:43:27,722 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:43:29,255 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:43:29,486 - root - INFO - Connected to Telegram servers
2025-07-18 04:43:29,555 - root - INFO - Sending code request...
2025-07-18 04:43:29,628 - telethon.client.users - INFO - Phone migrated to 4
2025-07-18 04:43:29,629 - telethon.client.telegrambaseclient - INFO - Reconnecting to new data center 4
2025-07-18 04:43:29,712 - telethon.network.mtprotosender - INFO - Disconnecting from **************:443/TcpFull...
2025-07-18 04:43:29,713 - telethon.network.mtprotosender - INFO - Disconnection from **************:443/TcpFull complete!
2025-07-18 04:43:29,715 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:43:31,284 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:43:31,736 - root - INFO - Code request sent successfully
2025-07-18 04:43:31,739 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:31] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:43:42,719 - root - ERROR - Failed to verify code: The asyncio event loop must not change after connection (see the FAQ for details)
2025-07-18 04:43:42,720 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:42] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:43:53,412 - root - ERROR - Failed to verify code: The asyncio event loop must not change after connection (see the FAQ for details)
2025-07-18 04:43:53,413 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:53] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:44:17,068 - root - ERROR - Failed to verify code: The asyncio event loop must not change after connection (see the FAQ for details)
2025-07-18 04:44:17,071 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:44:17] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:44:26,733 - root - ERROR - Failed to verify code: The asyncio event loop must not change after connection (see the FAQ for details)
2025-07-18 04:44:26,734 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:44:26] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:47:09,548 - telethon.crypto.libssl - INFO - Failed to load SSL library: <class 'OSError'> (no library called "ssl" found)
2025-07-18 04:47:09,550 - telethon.crypto.aes - INFO - cryptg module not installed and libssl not found, falling back to (slower) Python encryption
2025-07-18 04:47:10,324 - root - INFO - All dependencies are installed
2025-07-18 04:47:10,325 - root - INFO - Configuration is valid
2025-07-18 04:47:11,401 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:47:11,402 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:47:39,967 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:47:39] "GET / HTTP/1.1" 200 -
2025-07-18 04:47:40,059 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:47:40] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:47:40,121 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:47:40] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:47:42,110 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:47:42,172 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:47:42,450 - root - INFO - Connected to Telegram servers
2025-07-18 04:47:42,517 - root - INFO - Sending code request...
2025-07-18 04:47:42,603 - root - INFO - Code request sent successfully
2025-07-18 04:47:42,606 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:47:42] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:48:02,521 - root - INFO - Successfully authenticated with Telegram
2025-07-18 04:48:02,524 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:48:02] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:49:27,050 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:49:27,120 - root - ERROR - Error getting participants: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:49:27,123 - root - INFO - Extracted 0 members from Paid Udemy courses for free 2025
2025-07-18 04:49:27,129 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:49:27] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:49:59,066 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:49:59,178 - root - ERROR - Error getting participants: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:49:59,180 - root - INFO - Extracted 0 members from Paid Udemy courses for free 2025
2025-07-18 04:49:59,182 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:49:59] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:51:49,826 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:51:49,908 - root - ERROR - Error getting participants: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:51:49,910 - root - INFO - Extracted 0 members from Paid Udemy courses for free 2025
2025-07-18 04:51:49,911 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:51:49] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:56:34,654 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:56:34,655 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:57:07,988 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:07] "GET / HTTP/1.1" 200 -
2025-07-18 04:57:08,042 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:08] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:57:08,081 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:08] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:57:09,523 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:57:09,585 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:57:09,995 - root - INFO - Connected to Telegram servers
2025-07-18 04:57:09,996 - root - INFO - Already authorized
2025-07-18 04:57:09,997 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:09] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:57:13,023 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:57:13,023 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 04:57:13,182 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,185 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,187 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 04:57:13,270 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,273 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,274 - root - INFO - Attempting public group method...
2025-07-18 04:57:13,350 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,428 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,429 - root - WARNING - All extraction methods failed
2025-07-18 04:57:13,436 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:13] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:57:16,342 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:57:16,342 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 04:57:16,412 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,413 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,416 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 04:57:16,488 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,489 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,490 - root - INFO - Attempting public group method...
2025-07-18 04:57:16,572 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,650 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,658 - root - WARNING - All extraction methods failed
2025-07-18 04:57:16,660 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:16] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:59:41,049 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:59:41,050 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:00:01,694 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:01] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:00:04,195 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:04] "GET / HTTP/1.1" 200 -
2025-07-18 05:00:04,232 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:04] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:00:04,260 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:04] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:00:05,380 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:00:05,443 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:00:05,852 - root - INFO - Connected to Telegram servers
2025-07-18 05:00:05,853 - root - INFO - Already authorized
2025-07-18 05:00:05,856 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:05] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:00:08,393 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:00:08,393 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:00:08,510 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,511 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,511 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:00:08,631 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,632 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,632 - root - INFO - Attempting public group method...
2025-07-18 05:00:08,733 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,807 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,807 - root - INFO - Attempting message history method...
2025-07-18 05:00:08,808 - root - INFO - Scanning message history for active members...
2025-07-18 05:00:09,537 - root - ERROR - Error in message history method: 'Channel' object has no attribute 'bot'
2025-07-18 05:00:09,538 - root - WARNING - Message history method failed: 'Channel' object has no attribute 'bot'
2025-07-18 05:00:09,539 - root - INFO - Attempting recent activity method...
2025-07-18 05:00:09,614 - root - INFO - Attempting to get online members...
2025-07-18 05:00:09,685 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:09,687 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:00:14,215 - root - WARNING - Forwarded messages approach failed: 'Channel' object has no attribute 'bot'
2025-07-18 05:00:14,216 - root - WARNING - All extraction methods failed
2025-07-18 05:00:14,217 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:14] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:03:20,611 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:03:20,612 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:03:54,148 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:03:54] "GET / HTTP/1.1" 200 -
2025-07-18 05:03:54,203 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:03:54] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:03:54,257 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:03:54] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:03:55,362 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:03:55,426 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:03:55,813 - root - INFO - Connected to Telegram servers
2025-07-18 05:03:55,836 - root - INFO - Already authorized
2025-07-18 05:03:55,858 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:03:55] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:03:57,665 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:03:57,666 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:03:57,762 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:57,764 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:57,765 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:03:57,876 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:57,880 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:57,881 - root - INFO - Attempting public group method...
2025-07-18 05:03:58,035 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:58,231 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:58,232 - root - INFO - Attempting message history method...
2025-07-18 05:03:58,233 - root - INFO - Scanning message history for active members...
2025-07-18 05:04:00,146 - root - INFO - Scanned 100 messages, found 1 unique members
2025-07-18 05:04:02,126 - root - INFO - Scanned 200 messages, found 1 unique members
2025-07-18 05:04:02,130 - root - INFO - Message history scan complete: 1 members from 200 messages
2025-07-18 05:04:02,131 - root - INFO - Successfully extracted 1 members using message history method
2025-07-18 05:04:02,149 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:04:02] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:09:09,156 - telethon.client.updates - INFO - Got difference for account updates
2025-07-18 05:09:53,407 - root - INFO - Adding members to group: Inc
2025-07-18 05:09:53,493 - root - ERROR - Failed to add user udemy2020: Cannot cast InputPeerChannel to any kind of InputUser.
2025-07-18 05:09:53,494 - root - INFO - Member addition complete. Added: 0, Failed: 1
2025-07-18 05:09:53,495 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:09:53] "POST /api/transfer-members HTTP/1.1" 200 -
2025-07-18 05:12:12,428 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:12:12,428 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:12:56,959 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:12:56] "GET / HTTP/1.1" 200 -
2025-07-18 05:12:57,010 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:12:57] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:12:57,047 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:12:57] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:12:58,307 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:12:58,372 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:12:58,739 - root - INFO - Connected to Telegram servers
2025-07-18 05:12:58,740 - root - INFO - Already authorized
2025-07-18 05:12:58,741 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:12:58] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:13:00,322 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:13:00,322 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:13:00,400 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,408 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,409 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:13:00,479 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,480 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,481 - root - INFO - Attempting public group method...
2025-07-18 05:13:00,551 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,623 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,624 - root - INFO - Attempting message history method...
2025-07-18 05:13:00,624 - root - INFO - Scanning message history for active members...
2025-07-18 05:13:02,522 - root - INFO - Scanned 100 messages, found 0 unique members
2025-07-18 05:13:04,458 - root - INFO - Scanned 200 messages, found 0 unique members
2025-07-18 05:13:04,459 - root - INFO - Message history scan complete: 0 members from 200 messages
2025-07-18 05:13:04,460 - root - INFO - Attempting recent activity method...
2025-07-18 05:13:04,537 - root - INFO - Attempting to get online members...
2025-07-18 05:13:04,616 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:04,621 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:13:30,263 - root - ERROR - Error extracting members: 
2025-07-18 05:13:30,265 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:13:30] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:14:50,574 - root - WARNING - All extraction methods failed
2025-07-18 05:15:32,434 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:15:32,435 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:15:46,571 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:15:46] "GET / HTTP/1.1" 200 -
2025-07-18 05:15:46,637 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:15:46] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:15:46,729 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:15:46] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:15:47,788 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:15:47,852 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:15:48,253 - root - INFO - Connected to Telegram servers
2025-07-18 05:15:48,253 - root - INFO - Already authorized
2025-07-18 05:15:48,254 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:15:48] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:15:49,998 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:15:49,998 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:15:50,070 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:50,070 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:50,071 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:15:50,152 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:50,153 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:50,154 - root - INFO - Attempting public group method...
2025-07-18 05:15:50,230 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:50,327 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:50,328 - root - INFO - Attempting message history method...
2025-07-18 05:15:50,329 - root - INFO - Scanning message history for active members...
2025-07-18 05:15:51,313 - root - INFO - Message 1: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,314 - root - INFO - Message 2: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,314 - root - INFO - Message 3: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,314 - root - INFO - Message 4: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,315 - root - INFO - Message 5: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,315 - root - INFO - Message 6: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,315 - root - INFO - Message 7: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,316 - root - INFO - Message 8: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,316 - root - INFO - Message 9: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,316 - root - INFO - Message 10: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:52,825 - root - INFO - Scanned 100 messages, found 0 unique members
2025-07-18 05:15:56,133 - root - INFO - Scanned 200 messages, found 0 unique members
2025-07-18 05:15:56,642 - root - INFO - Message history scan complete: 0 members from 200 messages
2025-07-18 05:15:56,643 - root - INFO - Attempting recent activity method...
2025-07-18 05:15:56,719 - root - INFO - Attempting to get online members...
2025-07-18 05:15:56,801 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:56,802 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:15:58,442 - root - INFO - Scanned 50 forwarded/reply messages, found 0 members
2025-07-18 05:15:58,982 - root - INFO - Scanned 100 forwarded/reply messages, found 0 members
2025-07-18 05:16:00,120 - root - INFO - Scanned 150 forwarded/reply messages, found 0 members
2025-07-18 05:16:00,625 - root - INFO - Scanned 200 forwarded/reply messages, found 0 members
2025-07-18 05:16:00,627 - root - WARNING - All extraction methods failed
2025-07-18 05:16:00,632 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:16:00] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:18:13,247 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:18:13] "GET / HTTP/1.1" 200 -
2025-07-18 05:18:13,299 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:18:13] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:18:13,344 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:18:13] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:18:14,891 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:18:14,892 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:18:14,966 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:14,967 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:14,967 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:18:15,044 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:15,045 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:15,045 - root - INFO - Attempting public group method...
2025-07-18 05:18:15,127 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:15,199 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:15,200 - root - INFO - Attempting message history method...
2025-07-18 05:18:15,200 - root - INFO - Scanning message history for active members...
2025-07-18 05:18:15,965 - root - INFO - Message 1: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,965 - root - INFO - Message 2: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,966 - root - INFO - Message 3: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,966 - root - INFO - Message 4: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,966 - root - INFO - Message 5: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,967 - root - INFO - Message 6: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,967 - root - INFO - Message 7: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,970 - root - INFO - Message 8: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,972 - root - INFO - Message 9: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,972 - root - INFO - Message 10: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:17,493 - root - INFO - Scanned 100 messages, found 0 unique members
2025-07-18 05:18:20,393 - root - INFO - Scanned 200 messages, found 0 unique members
2025-07-18 05:18:20,897 - root - INFO - Message history scan complete: 0 members from 200 messages
2025-07-18 05:18:20,898 - root - INFO - Attempting recent activity method...
2025-07-18 05:18:21,014 - root - INFO - Attempting to get online members...
2025-07-18 05:18:21,199 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:21,199 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:18:22,496 - root - INFO - Scanned 50 forwarded/reply messages, found 0 members
2025-07-18 05:18:23,014 - root - INFO - Scanned 100 forwarded/reply messages, found 0 members
2025-07-18 05:18:24,130 - root - INFO - Scanned 150 forwarded/reply messages, found 0 members
2025-07-18 05:18:24,634 - root - INFO - Scanned 200 forwarded/reply messages, found 0 members
2025-07-18 05:18:24,636 - root - WARNING - All extraction methods failed
2025-07-18 05:18:24,639 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:18:24] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:20:52,238 - telethon.client.updates - INFO - Got difference for account updates
2025-07-18 05:22:13,077 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:22:13,077 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:22:24,115 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:22:24] "GET / HTTP/1.1" 200 -
2025-07-18 05:22:24,168 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:22:24] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:22:24,213 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:22:24] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:22:25,316 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:22:25,387 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:22:25,778 - root - INFO - Connected to Telegram servers
2025-07-18 05:22:25,778 - root - INFO - Already authorized
2025-07-18 05:22:25,780 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:22:25] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:22:28,078 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:22:28,079 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:22:28,150 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:28,151 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:28,159 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:22:28,238 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:28,241 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:28,243 - root - INFO - Attempting public group method...
2025-07-18 05:22:28,314 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:28,394 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:28,395 - root - INFO - Attempting message history method...
2025-07-18 05:22:28,395 - root - INFO - Scanning message history for active members...
2025-07-18 05:22:29,566 - root - INFO - Message 1: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,567 - root - INFO - Message 2: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,567 - root - INFO - Message 3: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,567 - root - INFO - Message 4: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,573 - root - INFO - Message 5: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,574 - root - INFO - Message 6: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,574 - root - INFO - Message 7: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,575 - root - INFO - Message 8: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,575 - root - INFO - Message 9: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,575 - root - INFO - Message 10: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:31,087 - root - INFO - Scanned 100 messages, found 0 unique members
2025-07-18 05:22:34,165 - root - INFO - Scanned 200 messages, found 0 unique members
2025-07-18 05:22:34,681 - root - INFO - Message history scan complete: 0 members from 200 messages
2025-07-18 05:22:34,682 - root - INFO - Attempting recent activity method...
2025-07-18 05:22:34,759 - root - INFO - Attempting to get online members...
2025-07-18 05:22:34,847 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:34,848 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:22:36,540 - root - INFO - Scanned 50 forwarded/reply messages, found 0 members
2025-07-18 05:22:37,045 - root - INFO - Scanned 100 forwarded/reply messages, found 0 members
2025-07-18 05:22:38,411 - root - INFO - Scanned 150 forwarded/reply messages, found 0 members
2025-07-18 05:22:38,919 - root - INFO - Scanned 200 forwarded/reply messages, found 0 members
2025-07-18 05:22:38,921 - root - INFO - Attempting admin extraction method...
2025-07-18 05:22:38,922 - root - INFO - Attempting to extract group admins...
2025-07-18 05:22:39,014 - root - WARNING - Direct admin extraction failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:39,021 - root - INFO - Scanning for admin signatures in messages...
2025-07-18 05:22:42,871 - root - INFO - Admin extraction complete: 0 admins found
2025-07-18 05:22:42,872 - root - INFO - Attempting reaction/view method for channels...
2025-07-18 05:22:42,873 - root - INFO - Scanning message reactions for users...
2025-07-18 05:22:58,008 - root - ERROR - Error extracting members: 
2025-07-18 05:22:58,026 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:22:58] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:23:14,333 - root - INFO - Reaction scan complete: 0 members from 100 messages
2025-07-18 05:23:14,334 - root - INFO - Attempting comment extraction method...
2025-07-18 05:23:14,335 - root - INFO - Scanning channel comments for users...
2025-07-18 05:23:14,749 - root - INFO - Message 4110 has 0 replies
2025-07-18 05:23:15,156 - root - INFO - Message 4101 has 0 replies
2025-07-18 05:23:15,578 - root - INFO - Message 3725 has 0 replies
2025-07-18 05:23:16,000 - root - INFO - Message 3724 has 0 replies
2025-07-18 05:23:16,419 - root - INFO - Message 3723 has 0 replies
2025-07-18 05:23:16,888 - root - INFO - Message 3722 has 2 replies
2025-07-18 05:23:17,321 - root - INFO - Message 3721 has 0 replies
2025-07-18 05:23:17,731 - root - INFO - Message 3718 has 0 replies
2025-07-18 05:23:18,156 - root - INFO - Message 3717 has 7 replies
2025-07-18 05:23:18,577 - root - INFO - Message 3716 has 3 replies
2025-07-18 05:23:18,986 - root - INFO - Message 3715 has 2 replies
2025-07-18 05:23:19,422 - root - INFO - Message 3713 has 6 replies
2025-07-18 05:23:19,849 - root - INFO - Message 3711 has 2 replies
2025-07-18 05:23:20,254 - root - INFO - Message 3710 has 2 replies
2025-07-18 05:23:20,722 - root - INFO - Message 3709 has 0 replies
2025-07-18 05:23:21,160 - root - INFO - Message 3708 has 0 replies
2025-07-18 05:23:21,581 - root - INFO - Message 3707 has 1 replies
2025-07-18 05:23:22,050 - root - INFO - Message 3706 has 2 replies
2025-07-18 05:23:22,614 - root - INFO - Message 3705 has 0 replies
2025-07-18 05:23:23,333 - root - INFO - Message 3703 has 3 replies
2025-07-18 05:23:23,741 - root - INFO - Message 3702 has 1 replies
2025-07-18 05:23:24,165 - root - INFO - Message 3700 has 1 replies
2025-07-18 05:23:24,572 - root - INFO - Message 3699 has 1 replies
2025-07-18 05:23:24,979 - root - INFO - Message 3698 has 0 replies
2025-07-18 05:23:25,383 - root - INFO - Message 3697 has 0 replies
2025-07-18 05:23:25,812 - root - INFO - Message 3696 has 1 replies
2025-07-18 05:23:26,223 - root - INFO - Message 3694 has 0 replies
2025-07-18 05:23:26,644 - root - INFO - Message 3693 has 0 replies
2025-07-18 05:23:27,048 - root - INFO - Message 3692 has 1 replies
2025-07-18 05:23:27,457 - root - INFO - Message 3690 has 2 replies
2025-07-18 05:23:27,865 - root - INFO - Message 3689 has 1 replies
2025-07-18 05:23:28,288 - root - INFO - Message 3687 has 1 replies
2025-07-18 05:23:28,709 - root - INFO - Message 3686 has 3 replies
2025-07-18 05:23:29,130 - root - INFO - Message 3685 has 2 replies
2025-07-18 05:23:29,550 - root - INFO - Message 3684 has 4 replies
2025-07-18 05:23:29,943 - root - INFO - Message 3683 has 10 replies
2025-07-18 05:23:30,365 - root - INFO - Message 3682 has 5 replies
2025-07-18 05:23:30,786 - root - INFO - Message 3681 has 1 replies
2025-07-18 05:23:31,302 - root - INFO - Message 3680 has 0 replies
2025-07-18 05:23:31,723 - root - INFO - Message 3679 has 0 replies
2025-07-18 05:23:32,146 - root - INFO - Message 3678 has 4 replies
2025-07-18 05:23:32,557 - root - INFO - Message 3677 has 6 replies
2025-07-18 05:23:32,967 - root - INFO - Message 3676 has 7 replies
2025-07-18 05:23:33,358 - root - INFO - Message 3675 has 1 replies
2025-07-18 05:23:33,909 - root - INFO - Message 3674 has 2 replies
2025-07-18 05:23:34,380 - root - INFO - Message 3673 has 1 replies
2025-07-18 05:23:35,763 - root - INFO - Comment scan complete: 0 members from 50 messages
2025-07-18 05:23:35,764 - root - WARNING - All extraction methods failed
2025-07-18 05:23:35,765 - root - INFO - Suggestion: Try a different group with more user interaction (discussion groups rather than broadcast channels)
2025-07-18 05:25:32,382 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:25:32,382 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:25:37,995 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:25:37] "GET / HTTP/1.1" 200 -
2025-07-18 05:25:38,047 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:25:38] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:25:38,113 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:25:38] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:25:39,014 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:25:39,076 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:25:39,446 - root - INFO - Connected to Telegram servers
2025-07-18 05:25:39,447 - root - INFO - Already authorized
2025-07-18 05:25:39,450 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:25:39] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:25:41,316 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:25:41,316 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:25:41,392 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:25:41,393 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:25:41,394 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:25:41,464 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:25:41,465 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:25:41,469 - root - INFO - Attempting public group method...
2025-07-18 05:25:41,542 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:25:41,613 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:25:41,613 - root - INFO - Attempting message history method...
2025-07-18 05:25:41,614 - root - INFO - Scanning message history for active members...
2025-07-18 05:25:42,490 - root - INFO - Message 1: Sender type: Channel, ID: 1215683262
2025-07-18 05:25:42,490 - root - INFO - Message 2: Sender type: Channel, ID: 1215683262
2025-07-18 05:25:42,491 - root - INFO - Message 3: Sender type: Channel, ID: 1215683262
2025-07-18 05:25:42,491 - root - INFO - Message 4: Sender type: Channel, ID: 1215683262
2025-07-18 05:25:42,492 - root - INFO - Message 5: Sender type: Channel, ID: 1215683262
2025-07-18 05:25:42,492 - root - INFO - Message 6: Sender type: Channel, ID: 1215683262
2025-07-18 05:25:42,492 - root - INFO - Message 7: Sender type: Channel, ID: 1215683262
2025-07-18 05:25:42,493 - root - INFO - Message 8: Sender type: Channel, ID: 1215683262
2025-07-18 05:25:42,493 - root - INFO - Message 9: Sender type: Channel, ID: 1215683262
2025-07-18 05:25:42,493 - root - INFO - Message 10: Sender type: Channel, ID: 1215683262
2025-07-18 05:25:44,014 - root - INFO - Scanned 100 messages, found 0 unique members
2025-07-18 05:25:47,013 - root - INFO - Scanned 200 messages, found 0 unique members
2025-07-18 05:25:47,516 - root - INFO - Message history scan complete: 0 members from 200 messages
2025-07-18 05:25:47,517 - root - INFO - Attempting recent activity method...
2025-07-18 05:25:47,588 - root - INFO - Attempting to get online members...
2025-07-18 05:25:47,658 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:25:47,658 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:25:48,866 - root - INFO - Scanned 50 forwarded/reply messages, found 0 members
2025-07-18 05:25:49,385 - root - INFO - Scanned 100 forwarded/reply messages, found 0 members
2025-07-18 05:25:50,544 - root - INFO - Scanned 150 forwarded/reply messages, found 0 members
2025-07-18 05:25:51,046 - root - INFO - Scanned 200 forwarded/reply messages, found 0 members
2025-07-18 05:25:51,048 - root - INFO - Attempting admin extraction method...
2025-07-18 05:25:51,049 - root - INFO - Attempting to extract group admins...
2025-07-18 05:25:51,138 - root - WARNING - Direct admin extraction failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:25:51,139 - root - INFO - Scanning for admin signatures in messages...
2025-07-18 05:25:54,556 - root - INFO - Admin extraction complete: 0 admins found
2025-07-18 05:25:54,557 - root - INFO - Attempting reaction/view method for channels...
2025-07-18 05:25:54,557 - root - INFO - Scanning message reactions for users...
2025-07-18 05:25:55,188 - root - INFO - Message 4110 has 22 total reactions
2025-07-18 05:25:55,400 - root - INFO - Message 4101 has 28 total reactions
2025-07-18 05:25:55,603 - root - INFO - Message 3725 has 38 total reactions
2025-07-18 05:25:55,805 - root - INFO - Message 3724 has 27 total reactions
2025-07-18 05:25:56,010 - root - INFO - Message 3723 has 17 total reactions
2025-07-18 05:25:56,213 - root - INFO - Message 3722 has 31 total reactions
2025-07-18 05:25:56,431 - root - INFO - Message 3721 has 24 total reactions
2025-07-18 05:25:56,635 - root - INFO - Message 3718 has 28 total reactions
2025-07-18 05:25:56,839 - root - INFO - Message 3717 has 27 total reactions
2025-07-18 05:25:57,041 - root - INFO - Message 3716 has 19 total reactions
2025-07-18 05:25:57,243 - root - INFO - Message 3715 has 20 total reactions
2025-07-18 05:25:57,448 - root - INFO - Message 3713 has 21 total reactions
2025-07-18 05:25:57,651 - root - INFO - Message 3711 has 15 total reactions
2025-07-18 05:25:57,854 - root - INFO - Message 3710 has 20 total reactions
2025-07-18 05:25:58,059 - root - INFO - Message 3709 has 28 total reactions
2025-07-18 05:25:58,262 - root - INFO - Message 3708 has 28 total reactions
2025-07-18 05:25:58,465 - root - INFO - Message 3707 has 43 total reactions
2025-07-18 05:25:58,667 - root - INFO - Message 3706 has 27 total reactions
2025-07-18 05:25:58,870 - root - INFO - Message 3705 has 27 total reactions
2025-07-18 05:25:59,273 - root - INFO - Message 3703 has 48 total reactions
2025-07-18 05:25:59,476 - root - INFO - Message 3702 has 36 total reactions
2025-07-18 05:25:59,680 - root - INFO - Message 3700 has 29 total reactions
2025-07-18 05:25:59,883 - root - INFO - Message 3699 has 21 total reactions
2025-07-18 05:26:00,086 - root - INFO - Message 3698 has 24 total reactions
2025-07-18 05:26:00,292 - root - INFO - Message 3697 has 31 total reactions
2025-07-18 05:26:00,498 - root - INFO - Message 3696 has 23 total reactions
2025-07-18 05:26:00,701 - root - INFO - Message 3694 has 19 total reactions
2025-07-18 05:26:00,905 - root - INFO - Message 3693 has 20 total reactions
2025-07-18 05:26:01,122 - root - INFO - Message 3692 has 34 total reactions
2025-07-18 05:26:01,325 - root - INFO - Message 3690 has 24 total reactions
2025-07-18 05:26:01,532 - root - INFO - Message 3689 has 30 total reactions
2025-07-18 05:26:01,734 - root - INFO - Message 3687 has 30 total reactions
2025-07-18 05:26:01,940 - root - INFO - Message 3686 has 23 total reactions
2025-07-18 05:26:02,143 - root - INFO - Message 3685 has 27 total reactions
2025-07-18 05:26:02,348 - root - INFO - Message 3684 has 42 total reactions
2025-07-18 05:26:02,550 - root - INFO - Message 3683 has 31 total reactions
2025-07-18 05:26:02,755 - root - INFO - Message 3682 has 20 total reactions
2025-07-18 05:26:02,959 - root - INFO - Message 3681 has 27 total reactions
2025-07-18 05:26:03,175 - root - INFO - Message 3680 has 22 total reactions
2025-07-18 05:26:03,377 - root - INFO - Message 3679 has 28 total reactions
2025-07-18 05:26:03,587 - root - INFO - Message 3678 has 26 total reactions
2025-07-18 05:26:03,803 - root - INFO - Message 3677 has 26 total reactions
2025-07-18 05:26:04,007 - root - INFO - Message 3676 has 33 total reactions
2025-07-18 05:26:04,211 - root - INFO - Message 3675 has 34 total reactions
2025-07-18 05:26:04,415 - root - INFO - Message 3674 has 36 total reactions
2025-07-18 05:26:04,633 - root - INFO - Message 3673 has 45 total reactions
2025-07-18 05:26:05,039 - root - INFO - Message 3670 has 40 total reactions
2025-07-18 05:26:05,242 - root - INFO - Message 3669 has 30 total reactions
2025-07-18 05:26:05,442 - root - INFO - Message 3668 has 23 total reactions
2025-07-18 05:26:05,648 - root - INFO - Message 3663 has 28 total reactions
2025-07-18 05:26:05,849 - root - INFO - Message 3662 has 31 total reactions
2025-07-18 05:26:06,052 - root - INFO - Message 3661 has 19 total reactions
2025-07-18 05:26:06,254 - root - INFO - Message 3660 has 17 total reactions
2025-07-18 05:26:06,459 - root - INFO - Message 3658 has 24 total reactions
2025-07-18 05:26:06,667 - root - INFO - Message 3657 has 14 total reactions
2025-07-18 05:26:06,882 - root - INFO - Message 3655 has 20 total reactions
2025-07-18 05:26:07,085 - root - INFO - Message 3653 has 27 total reactions
2025-07-18 05:26:07,290 - root - INFO - Message 3652 has 22 total reactions
2025-07-18 05:26:07,492 - root - INFO - Message 3651 has 29 total reactions
2025-07-18 05:26:07,697 - root - INFO - Message 3650 has 31 total reactions
2025-07-18 05:26:07,901 - root - INFO - Message 3649 has 23 total reactions
2025-07-18 05:26:08,104 - root - INFO - Message 3648 has 26 total reactions
2025-07-18 05:26:08,309 - root - INFO - Message 3647 has 24 total reactions
2025-07-18 05:26:08,512 - root - INFO - Message 3646 has 35 total reactions
2025-07-18 05:26:08,715 - root - INFO - Message 3645 has 21 total reactions
2025-07-18 05:26:08,919 - root - INFO - Message 3644 has 31 total reactions
2025-07-18 05:26:09,126 - root - INFO - Message 3643 has 33 total reactions
2025-07-18 05:26:09,329 - root - INFO - Message 3642 has 23 total reactions
2025-07-18 05:26:09,534 - root - INFO - Message 3641 has 29 total reactions
2025-07-18 05:26:09,738 - root - INFO - Message 3640 has 22 total reactions
2025-07-18 05:26:09,942 - root - INFO - Message 3639 has 23 total reactions
2025-07-18 05:26:10,159 - root - INFO - Message 3638 has 38 total reactions
2025-07-18 05:26:10,360 - root - INFO - Message 3637 has 34 total reactions
2025-07-18 05:26:10,564 - root - INFO - Message 3636 has 19 total reactions
2025-07-18 05:26:10,766 - root - INFO - Message 3634 has 32 total reactions
2025-07-18 05:26:10,971 - root - INFO - Message 3633 has 26 total reactions
2025-07-18 05:26:11,178 - root - INFO - Message 3632 has 25 total reactions
2025-07-18 05:26:11,251 - root - ERROR - Error extracting members: 
2025-07-18 05:26:11,252 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:26:11] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:26:11,394 - root - INFO - Message 3631 has 18 total reactions
2025-07-18 05:26:11,596 - root - INFO - Message 3630 has 26 total reactions
2025-07-18 05:26:11,799 - root - INFO - Message 3629 has 21 total reactions
2025-07-18 05:26:12,002 - root - INFO - Message 3628 has 26 total reactions
2025-07-18 05:26:12,420 - root - INFO - Message 3626 has 31 total reactions
2025-07-18 05:26:12,625 - root - INFO - Message 3625 has 26 total reactions
2025-07-18 05:26:13,031 - root - INFO - Message 3623 has 16 total reactions
2025-07-18 05:26:13,233 - root - INFO - Message 3622 has 37 total reactions
2025-07-18 05:26:13,436 - root - INFO - Message 3621 has 31 total reactions
2025-07-18 05:26:13,639 - root - INFO - Message 3620 has 27 total reactions
2025-07-18 05:26:13,841 - root - INFO - Message 3619 has 25 total reactions
2025-07-18 05:26:14,046 - root - INFO - Message 3618 has 30 total reactions
2025-07-18 05:26:14,248 - root - INFO - Message 3617 has 33 total reactions
2025-07-18 05:26:14,464 - root - INFO - Message 3616 has 16 total reactions
2025-07-18 05:26:14,668 - root - INFO - Message 3615 has 24 total reactions
2025-07-18 05:26:14,871 - root - INFO - Message 3613 has 28 total reactions
2025-07-18 05:26:15,085 - root - INFO - Message 3612 has 20 total reactions
2025-07-18 05:26:15,503 - root - INFO - Reaction scan complete: 0 members from 100 messages
2025-07-18 05:26:15,505 - root - INFO - Attempting comment extraction method...
2025-07-18 05:26:15,506 - root - INFO - Scanning channel comments for users...
2025-07-18 05:26:15,849 - root - INFO - Message 4110 has 0 replies
2025-07-18 05:26:16,237 - root - INFO - Message 4101 has 0 replies
2025-07-18 05:26:16,656 - root - INFO - Message 3725 has 0 replies
2025-07-18 05:26:17,045 - root - INFO - Message 3724 has 0 replies
2025-07-18 05:26:17,449 - root - INFO - Message 3723 has 0 replies
2025-07-18 05:26:17,855 - root - INFO - Message 3722 has 2 replies
2025-07-18 05:26:18,308 - root - INFO - Message 3721 has 0 replies
2025-07-18 05:26:18,712 - root - INFO - Message 3718 has 0 replies
2025-07-18 05:26:19,134 - root - INFO - Message 3717 has 7 replies
2025-07-18 05:26:19,540 - root - INFO - Message 3716 has 3 replies
2025-07-18 05:26:19,965 - root - INFO - Message 3715 has 2 replies
2025-07-18 05:26:20,359 - root - INFO - Message 3713 has 6 replies
2025-07-18 05:26:20,766 - root - INFO - Message 3711 has 2 replies
2025-07-18 05:26:21,172 - root - INFO - Message 3710 has 2 replies
2025-07-18 05:26:21,596 - root - INFO - Message 3709 has 0 replies
2025-07-18 05:26:22,018 - root - INFO - Message 3708 has 0 replies
2025-07-18 05:26:22,423 - root - INFO - Message 3707 has 1 replies
2025-07-18 05:26:22,877 - root - INFO - Message 3706 has 2 replies
2025-07-18 05:26:23,296 - root - INFO - Message 3705 has 0 replies
2025-07-18 05:26:24,015 - root - INFO - Message 3703 has 3 replies
2025-07-18 05:26:24,510 - root - INFO - Message 3702 has 1 replies
2025-07-18 05:26:24,932 - root - INFO - Message 3700 has 1 replies
2025-07-18 05:26:25,352 - root - INFO - Message 3699 has 1 replies
2025-07-18 05:26:25,777 - root - INFO - Message 3698 has 0 replies
2025-07-18 05:26:26,197 - root - INFO - Message 3697 has 0 replies
2025-07-18 05:26:26,620 - root - INFO - Message 3696 has 1 replies
2025-07-18 05:26:27,058 - root - INFO - Message 3694 has 0 replies
2025-07-18 05:26:27,464 - root - INFO - Message 3693 has 0 replies
2025-07-18 05:26:27,887 - root - INFO - Message 3692 has 1 replies
2025-07-18 05:26:28,309 - root - INFO - Message 3690 has 2 replies
2025-07-18 05:26:28,729 - root - INFO - Message 3689 has 1 replies
2025-07-18 05:26:29,229 - root - INFO - Message 3687 has 1 replies
2025-07-18 05:26:29,652 - root - INFO - Message 3686 has 3 replies
2025-07-18 05:26:29,851 - telethon.client.updates - INFO - Got difference for account updates
2025-07-18 05:26:30,135 - root - INFO - Message 3685 has 2 replies
2025-07-18 05:26:30,558 - root - INFO - Message 3684 has 4 replies
2025-07-18 05:26:30,963 - root - INFO - Message 3683 has 10 replies
2025-07-18 05:26:31,367 - root - INFO - Message 3682 has 5 replies
2025-07-18 05:26:31,772 - root - INFO - Message 3681 has 1 replies
2025-07-18 05:26:32,179 - root - INFO - Message 3680 has 0 replies
2025-07-18 05:26:32,601 - root - INFO - Message 3679 has 0 replies
2025-07-18 05:26:33,005 - root - INFO - Message 3678 has 4 replies
2025-07-18 05:26:33,317 - root - INFO - Extracting members from group: Inc
2025-07-18 05:26:33,318 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:26:33,398 - root - INFO - Message 3677 has 6 replies
2025-07-18 05:26:33,788 - root - INFO - Message 3676 has 7 replies
2025-07-18 05:26:34,180 - root - INFO - Message 3675 has 1 replies
2025-07-18 05:26:34,468 - root - INFO - Successfully extracted 3 members using admin method
2025-07-18 05:26:34,469 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:26:34] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:26:34,584 - root - INFO - Message 3674 has 2 replies
2025-07-18 05:26:34,990 - root - INFO - Message 3673 has 1 replies
2025-07-18 05:26:36,323 - root - INFO - Comment scan complete: 0 members from 50 messages
2025-07-18 05:26:36,324 - root - WARNING - All extraction methods failed
2025-07-18 05:26:36,324 - root - INFO - Suggestion: Try a different group with more user interaction (discussion groups rather than broadcast channels)
2025-07-18 05:27:00,163 - root - INFO - Adding members to group: Intec29
2025-07-18 05:27:00,338 - root - INFO - Added user None to group
2025-07-18 05:27:02,521 - root - INFO - Added user None to group
2025-07-18 05:27:04,808 - root - INFO - Added user None to group
2025-07-18 05:27:06,813 - root - INFO - Member addition complete. Added: 3, Failed: 0
2025-07-18 05:27:06,816 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:27:06] "POST /api/transfer-members HTTP/1.1" 200 -
2025-07-18 05:27:20,896 - telethon.client.updates - INFO - Got difference for account updates
2025-07-18 05:27:59,850 - telethon.client.updates - INFO - Got difference for account updates
2025-07-18 05:28:08,689 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:28:08] "POST /api/send-bulk-messages HTTP/1.1" 200 -
2025-07-18 05:28:23,251 - root - INFO - Starting bulk message send to 3 recipients
2025-07-18 05:28:23,439 - root - INFO - Message sent to None
2025-07-18 05:28:25,605 - root - INFO - Message sent to None
2025-07-18 05:28:27,766 - root - INFO - Message sent to None
2025-07-18 05:28:29,778 - root - INFO - Bulk messaging complete. Sent: 3, Failed: 0
2025-07-18 05:28:29,782 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:28:29] "POST /api/send-bulk-messages HTTP/1.1" 200 -
2025-07-18 05:28:59,837 - telethon.client.updates - INFO - Got difference for account updates
2025-07-18 05:32:33,690 - telethon.client.updates - INFO - Got difference for account updates
2025-07-18 05:34:23,003 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:34:23,003 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:34:30,192 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:34:30] "GET / HTTP/1.1" 200 -
2025-07-18 05:34:30,261 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:34:30] "GET /static/app.js HTTP/1.1" 200 -
2025-07-18 05:34:30,336 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:34:30] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-18 05:34:30,342 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:34:30] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:34:31,641 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:34:31,708 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:34:32,088 - root - INFO - Connected to Telegram servers
2025-07-18 05:34:32,088 - root - INFO - Already authorized
2025-07-18 05:34:32,090 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:34:32] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:34:54,235 - root - INFO - Starting bulk message send to 1 groups
2025-07-18 05:34:54,401 - root - INFO - Message sent to group: Intec29
2025-07-18 05:34:56,416 - root - INFO - Group bulk messaging complete. Sent: 1, Failed: 0
2025-07-18 05:34:56,417 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:34:56] "POST /api/send-bulk-messages-to-groups HTTP/1.1" 200 -
2025-07-18 05:35:24,311 - root - INFO - Starting bulk message send to 1 groups
2025-07-18 05:35:24,312 - root - ERROR - Failed to send message to group @intec20,@inct2020: Cannot find any entity corresponding to "@intec20,@inct2020"
2025-07-18 05:35:24,312 - root - INFO - Group bulk messaging complete. Sent: 0, Failed: 1
2025-07-18 05:35:24,313 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:35:24] "POST /api/send-bulk-messages-to-groups HTTP/1.1" 200 -
2025-07-18 05:35:32,058 - root - INFO - Starting bulk message send to 2 groups
2025-07-18 05:35:32,423 - root - INFO - Message sent to group: Intec29
2025-07-18 05:35:34,615 - root - INFO - Message sent to group: Inc
2025-07-18 05:35:36,626 - root - INFO - Group bulk messaging complete. Sent: 2, Failed: 0
2025-07-18 05:35:36,627 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:35:36] "POST /api/send-bulk-messages-to-groups HTTP/1.1" 200 -
2025-07-18 05:37:13,266 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:37:13,267 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:37:13,343 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:37:13,344 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:37:13,344 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:37:13,413 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:37:13,414 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:37:13,414 - root - INFO - Attempting public group method...
2025-07-18 05:37:13,483 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:37:13,551 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:37:13,552 - root - INFO - Attempting message history method...
2025-07-18 05:37:13,552 - root - INFO - Scanning message history for active members...
2025-07-18 05:37:14,515 - root - INFO - Message 1: Sender type: Channel, ID: 1215683262
2025-07-18 05:37:14,515 - root - INFO - Message 2: Sender type: Channel, ID: 1215683262
2025-07-18 05:37:14,516 - root - INFO - Message 3: Sender type: Channel, ID: 1215683262
2025-07-18 05:37:14,516 - root - INFO - Message 4: Sender type: Channel, ID: 1215683262
2025-07-18 05:37:14,516 - root - INFO - Message 5: Sender type: Channel, ID: 1215683262
2025-07-18 05:37:14,517 - root - INFO - Message 6: Sender type: Channel, ID: 1215683262
2025-07-18 05:37:14,517 - root - INFO - Message 7: Sender type: Channel, ID: 1215683262
2025-07-18 05:37:14,517 - root - INFO - Message 8: Sender type: Channel, ID: 1215683262
2025-07-18 05:37:14,517 - root - INFO - Message 9: Sender type: Channel, ID: 1215683262
2025-07-18 05:37:14,518 - root - INFO - Message 10: Sender type: Channel, ID: 1215683262
2025-07-18 05:37:16,043 - root - INFO - Scanned 100 messages, found 0 unique members
2025-07-18 05:37:19,014 - root - INFO - Scanned 200 messages, found 0 unique members
2025-07-18 05:37:19,520 - root - INFO - Message history scan complete: 0 members from 200 messages
2025-07-18 05:37:19,521 - root - INFO - Attempting recent activity method...
2025-07-18 05:37:19,594 - root - INFO - Attempting to get online members...
2025-07-18 05:37:19,665 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:37:19,666 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:37:20,712 - root - INFO - Scanned 50 forwarded/reply messages, found 0 members
2025-07-18 05:37:21,215 - root - INFO - Scanned 100 forwarded/reply messages, found 0 members
2025-07-18 05:37:22,470 - root - INFO - Scanned 150 forwarded/reply messages, found 0 members
2025-07-18 05:37:22,972 - root - INFO - Scanned 200 forwarded/reply messages, found 0 members
2025-07-18 05:37:22,974 - root - INFO - Attempting admin extraction method...
2025-07-18 05:37:22,975 - root - INFO - Attempting to extract group admins...
2025-07-18 05:37:23,058 - root - WARNING - Direct admin extraction failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:37:23,059 - root - INFO - Scanning for admin signatures in messages...
2025-07-18 05:37:26,513 - root - INFO - Admin extraction complete: 0 admins found
2025-07-18 05:37:26,514 - root - INFO - Attempting reaction/view method for channels...
2025-07-18 05:37:26,514 - root - INFO - Scanning message reactions for users...
2025-07-18 05:37:27,181 - root - INFO - Message 4110 has 22 total reactions
2025-07-18 05:37:27,388 - root - INFO - Message 4101 has 28 total reactions
2025-07-18 05:37:27,591 - root - INFO - Message 3725 has 38 total reactions
2025-07-18 05:37:27,794 - root - INFO - Message 3724 has 27 total reactions
2025-07-18 05:37:28,010 - root - INFO - Message 3723 has 17 total reactions
2025-07-18 05:37:28,213 - root - INFO - Message 3722 has 31 total reactions
2025-07-18 05:37:28,418 - root - INFO - Message 3721 has 24 total reactions
2025-07-18 05:37:28,619 - root - INFO - Message 3718 has 28 total reactions
2025-07-18 05:37:28,827 - root - INFO - Message 3717 has 27 total reactions
2025-07-18 05:37:29,036 - root - INFO - Message 3716 has 19 total reactions
2025-07-18 05:37:29,281 - root - INFO - Message 3715 has 20 total reactions
2025-07-18 05:37:29,485 - root - INFO - Message 3713 has 21 total reactions
2025-07-18 05:37:29,687 - root - INFO - Message 3711 has 15 total reactions
2025-07-18 05:37:29,890 - root - INFO - Message 3710 has 20 total reactions
2025-07-18 05:37:30,094 - root - INFO - Message 3709 has 28 total reactions
2025-07-18 05:37:30,298 - root - INFO - Message 3708 has 28 total reactions
2025-07-18 05:37:30,504 - root - INFO - Message 3707 has 43 total reactions
2025-07-18 05:37:30,707 - root - INFO - Message 3706 has 27 total reactions
2025-07-18 05:37:30,911 - root - INFO - Message 3705 has 27 total reactions
2025-07-18 05:37:31,319 - root - INFO - Message 3703 has 48 total reactions
2025-07-18 05:37:31,521 - root - INFO - Message 3702 has 36 total reactions
2025-07-18 05:37:31,724 - root - INFO - Message 3700 has 29 total reactions
2025-07-18 05:37:31,929 - root - INFO - Message 3699 has 21 total reactions
2025-07-18 05:37:32,133 - root - INFO - Message 3698 has 24 total reactions
2025-07-18 05:37:32,336 - root - INFO - Message 3697 has 31 total reactions
2025-07-18 05:37:32,540 - root - INFO - Message 3696 has 23 total reactions
2025-07-18 05:37:32,746 - root - INFO - Message 3694 has 19 total reactions
2025-07-18 05:37:32,951 - root - INFO - Message 3693 has 20 total reactions
2025-07-18 05:37:33,154 - root - INFO - Message 3692 has 34 total reactions
2025-07-18 05:37:33,360 - root - INFO - Message 3690 has 24 total reactions
2025-07-18 05:37:33,566 - root - INFO - Message 3689 has 30 total reactions
2025-07-18 05:37:33,784 - root - INFO - Message 3687 has 30 total reactions
2025-07-18 05:37:33,988 - root - INFO - Message 3686 has 23 total reactions
2025-07-18 05:37:34,190 - root - INFO - Message 3685 has 27 total reactions
2025-07-18 05:37:34,394 - root - INFO - Message 3684 has 42 total reactions
2025-07-18 05:37:34,596 - root - INFO - Message 3683 has 31 total reactions
2025-07-18 05:37:34,801 - root - INFO - Message 3682 has 20 total reactions
2025-07-18 05:37:35,007 - root - INFO - Message 3681 has 27 total reactions
2025-07-18 05:37:35,212 - root - INFO - Message 3680 has 22 total reactions
2025-07-18 05:37:35,415 - root - INFO - Message 3679 has 28 total reactions
2025-07-18 05:37:35,618 - root - INFO - Message 3678 has 26 total reactions
2025-07-18 05:37:35,823 - root - INFO - Message 3677 has 26 total reactions
2025-07-18 05:37:36,026 - root - INFO - Message 3676 has 33 total reactions
2025-07-18 05:37:36,229 - root - INFO - Message 3675 has 34 total reactions
2025-07-18 05:37:36,433 - root - INFO - Message 3674 has 36 total reactions
2025-07-18 05:37:36,635 - root - INFO - Message 3673 has 45 total reactions
2025-07-18 05:37:37,041 - root - INFO - Message 3670 has 40 total reactions
2025-07-18 05:37:37,247 - root - INFO - Message 3669 has 30 total reactions
2025-07-18 05:37:37,449 - root - INFO - Message 3668 has 23 total reactions
2025-07-18 05:37:37,654 - root - INFO - Message 3663 has 28 total reactions
2025-07-18 05:37:37,855 - root - INFO - Message 3662 has 31 total reactions
2025-07-18 05:37:38,059 - root - INFO - Message 3661 has 19 total reactions
2025-07-18 05:37:38,261 - root - INFO - Message 3660 has 17 total reactions
2025-07-18 05:37:38,462 - root - INFO - Message 3658 has 24 total reactions
2025-07-18 05:37:38,678 - root - INFO - Message 3657 has 14 total reactions
2025-07-18 05:37:38,882 - root - INFO - Message 3655 has 20 total reactions
2025-07-18 05:37:39,089 - root - INFO - Message 3653 has 27 total reactions
2025-07-18 05:37:39,307 - root - INFO - Message 3652 has 22 total reactions
2025-07-18 05:37:39,510 - root - INFO - Message 3651 has 29 total reactions
2025-07-18 05:37:39,715 - root - INFO - Message 3650 has 31 total reactions
2025-07-18 05:37:39,918 - root - INFO - Message 3649 has 23 total reactions
2025-07-18 05:37:40,121 - root - INFO - Message 3648 has 26 total reactions
2025-07-18 05:37:40,324 - root - INFO - Message 3647 has 24 total reactions
2025-07-18 05:37:40,525 - root - INFO - Message 3646 has 35 total reactions
2025-07-18 05:37:40,727 - root - INFO - Message 3645 has 21 total reactions
2025-07-18 05:37:40,931 - root - INFO - Message 3644 has 31 total reactions
2025-07-18 05:37:41,136 - root - INFO - Message 3643 has 33 total reactions
2025-07-18 05:37:41,338 - root - INFO - Message 3642 has 23 total reactions
2025-07-18 05:37:41,543 - root - INFO - Message 3641 has 29 total reactions
2025-07-18 05:37:41,747 - root - INFO - Message 3640 has 22 total reactions
2025-07-18 05:37:41,950 - root - INFO - Message 3639 has 23 total reactions
2025-07-18 05:37:42,151 - root - INFO - Message 3638 has 38 total reactions
2025-07-18 05:37:42,355 - root - INFO - Message 3637 has 34 total reactions
2025-07-18 05:37:42,559 - root - INFO - Message 3636 has 19 total reactions
2025-07-18 05:37:42,765 - root - INFO - Message 3634 has 32 total reactions
2025-07-18 05:37:42,968 - root - INFO - Message 3633 has 26 total reactions
2025-07-18 05:37:43,172 - root - INFO - Message 3632 has 25 total reactions
2025-07-18 05:37:43,204 - root - ERROR - Error extracting members: 
2025-07-18 05:37:43,205 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:37:43] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:37:43,375 - root - INFO - Message 3631 has 18 total reactions
2025-07-18 05:37:43,578 - root - INFO - Message 3630 has 26 total reactions
2025-07-18 05:37:43,781 - root - INFO - Message 3629 has 21 total reactions
2025-07-18 05:37:43,984 - root - INFO - Message 3628 has 26 total reactions
2025-07-18 05:37:44,390 - root - INFO - Message 3626 has 31 total reactions
2025-07-18 05:37:44,592 - root - INFO - Message 3625 has 26 total reactions
2025-07-18 05:37:45,001 - root - INFO - Message 3623 has 16 total reactions
2025-07-18 05:37:45,206 - root - INFO - Message 3622 has 37 total reactions
2025-07-18 05:37:45,417 - root - INFO - Message 3621 has 31 total reactions
2025-07-18 05:37:45,632 - root - INFO - Message 3620 has 27 total reactions
2025-07-18 05:37:45,886 - root - INFO - Message 3619 has 25 total reactions
2025-07-18 05:37:46,091 - root - INFO - Message 3618 has 30 total reactions
2025-07-18 05:37:46,297 - root - INFO - Message 3617 has 33 total reactions
2025-07-18 05:37:46,500 - root - INFO - Message 3616 has 16 total reactions
2025-07-18 05:37:46,705 - root - INFO - Message 3615 has 24 total reactions
2025-07-18 05:37:46,911 - root - INFO - Message 3613 has 28 total reactions
2025-07-18 05:37:47,116 - root - INFO - Message 3612 has 20 total reactions
2025-07-18 05:37:47,526 - root - INFO - Reaction scan complete: 0 members from 100 messages
2025-07-18 05:37:47,527 - root - INFO - Attempting comment extraction method...
2025-07-18 05:37:47,528 - root - INFO - Scanning channel comments for users...
2025-07-18 05:37:47,958 - root - INFO - Message 4110 has 0 replies
2025-07-18 05:37:48,362 - root - INFO - Message 4101 has 0 replies
2025-07-18 05:37:48,754 - root - INFO - Message 3725 has 0 replies
2025-07-18 05:37:49,148 - root - INFO - Message 3724 has 0 replies
2025-07-18 05:37:49,571 - root - INFO - Message 3723 has 0 replies
2025-07-18 05:37:49,961 - root - INFO - Message 3722 has 2 replies
2025-07-18 05:37:50,386 - root - INFO - Message 3721 has 0 replies
2025-07-18 05:37:50,807 - root - INFO - Message 3718 has 0 replies
2025-07-18 05:37:51,245 - root - INFO - Message 3717 has 7 replies
2025-07-18 05:37:51,650 - root - INFO - Message 3716 has 3 replies
2025-07-18 05:37:52,058 - root - INFO - Message 3715 has 2 replies
2025-07-18 05:37:52,482 - root - INFO - Message 3713 has 6 replies
2025-07-18 05:37:52,888 - root - INFO - Message 3711 has 2 replies
2025-07-18 05:37:53,326 - root - INFO - Message 3710 has 2 replies
2025-07-18 05:37:53,744 - root - INFO - Message 3709 has 0 replies
2025-07-18 05:37:54,166 - root - INFO - Message 3708 has 0 replies
2025-07-18 05:37:54,574 - root - INFO - Message 3707 has 1 replies
2025-07-18 05:37:54,966 - root - INFO - Message 3706 has 2 replies
2025-07-18 05:37:55,372 - root - INFO - Message 3705 has 0 replies
2025-07-18 05:37:56,105 - root - INFO - Message 3703 has 3 replies
2025-07-18 05:37:56,496 - root - INFO - Message 3702 has 1 replies
2025-07-18 05:37:56,902 - root - INFO - Message 3700 has 1 replies
2025-07-18 05:37:57,325 - root - INFO - Message 3699 has 1 replies
2025-07-18 05:37:57,764 - root - INFO - Message 3698 has 0 replies
2025-07-18 05:37:58,158 - root - INFO - Message 3697 has 0 replies
2025-07-18 05:37:58,565 - root - INFO - Message 3696 has 1 replies
2025-07-18 05:37:58,989 - root - INFO - Message 3694 has 0 replies
2025-07-18 05:37:59,380 - root - INFO - Message 3693 has 0 replies
2025-07-18 05:37:59,772 - root - INFO - Message 3692 has 1 replies
2025-07-18 05:38:00,190 - root - INFO - Message 3690 has 2 replies
2025-07-18 05:38:00,597 - root - INFO - Message 3689 has 1 replies
2025-07-18 05:38:00,988 - root - INFO - Message 3687 has 1 replies
2025-07-18 05:38:01,408 - root - INFO - Message 3686 has 3 replies
2025-07-18 05:38:01,798 - root - INFO - Message 3685 has 2 replies
2025-07-18 05:38:02,219 - root - INFO - Message 3684 has 4 replies
2025-07-18 05:38:02,619 - root - INFO - Message 3683 has 10 replies
2025-07-18 05:38:03,071 - root - INFO - Message 3682 has 5 replies
2025-07-18 05:38:03,493 - root - INFO - Message 3681 has 1 replies
2025-07-18 05:38:03,901 - root - INFO - Message 3680 has 0 replies
2025-07-18 05:38:04,312 - root - INFO - Message 3679 has 0 replies
2025-07-18 05:38:04,719 - root - INFO - Message 3678 has 4 replies
2025-07-18 05:38:05,111 - root - INFO - Message 3677 has 6 replies
2025-07-18 05:38:05,522 - root - INFO - Message 3676 has 7 replies
2025-07-18 05:38:05,927 - root - INFO - Message 3675 has 1 replies
2025-07-18 05:38:06,317 - root - INFO - Message 3674 has 2 replies
2025-07-18 05:38:06,721 - root - INFO - Message 3673 has 1 replies
2025-07-18 05:38:08,062 - root - INFO - Comment scan complete: 0 members from 50 messages
2025-07-18 05:38:08,063 - root - WARNING - All extraction methods failed
2025-07-18 05:38:08,063 - root - INFO - Suggestion: Try a different group with more user interaction (discussion groups rather than broadcast channels)
2025-07-18 05:41:40,131 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:41:40,132 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
