2025-07-18 04:37:11,491 - telethon.crypto.libssl - INFO - Failed to load SSL library: <class 'OSError'> (no library called "ssl" found)
2025-07-18 04:37:11,493 - telethon.crypto.aes - INFO - cryptg module not installed and libssl not found, falling back to (slower) Python encryption
2025-07-18 04:37:12,537 - root - INFO - All dependencies are installed
2025-07-18 04:37:12,538 - root - INFO - Configuration is valid
2025-07-18 04:37:12,539 - root - INFO - Created directory: sessions
2025-07-18 04:37:12,606 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:37:12,607 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:37:32,853 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:32] "GET / HTTP/1.1" 200 -
2025-07-18 04:37:32,964 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:32] "GET /static/app.js HTTP/1.1" 200 -
2025-07-18 04:37:33,592 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:33] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:37:33,810 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:33] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-18 04:37:36,516 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:37:38,185 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:37:38,602 - root - ERROR - Failed to initialize Telegram client: The api_id/api_hash combination is invalid (caused by SendCodeRequest)
2025-07-18 04:37:38,604 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:38] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:37:46,212 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:37:46,279 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:37:47,003 - root - ERROR - Failed to initialize Telegram client: The api_id/api_hash combination is invalid (caused by SendCodeRequest)
2025-07-18 04:37:47,007 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:47] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:39:50,790 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:50] "GET / HTTP/1.1" 200 -
2025-07-18 04:39:50,952 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:50] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:39:51,135 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:51] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:39:53,828 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:39:53,894 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:39:54,295 - root - ERROR - Failed to initialize Telegram client: The api_id/api_hash combination is invalid (caused by SendCodeRequest)
2025-07-18 04:39:54,297 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:54] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:42:48,179 - telethon.crypto.libssl - INFO - Failed to load SSL library: <class 'OSError'> (no library called "ssl" found)
2025-07-18 04:42:48,181 - telethon.crypto.aes - INFO - cryptg module not installed and libssl not found, falling back to (slower) Python encryption
2025-07-18 04:42:49,077 - root - INFO - All dependencies are installed
2025-07-18 04:42:49,077 - root - INFO - Configuration is valid
2025-07-18 04:42:49,137 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:42:49,138 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:43:24,194 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:24] "GET / HTTP/1.1" 200 -
2025-07-18 04:43:24,257 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:24] "GET /static/app.js HTTP/1.1" 200 -
2025-07-18 04:43:24,330 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:24] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:43:26,081 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:26] "GET / HTTP/1.1" 200 -
2025-07-18 04:43:26,139 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:26] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:43:26,161 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:26] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:43:27,722 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:43:29,255 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:43:29,486 - root - INFO - Connected to Telegram servers
2025-07-18 04:43:29,555 - root - INFO - Sending code request...
2025-07-18 04:43:29,628 - telethon.client.users - INFO - Phone migrated to 4
2025-07-18 04:43:29,629 - telethon.client.telegrambaseclient - INFO - Reconnecting to new data center 4
2025-07-18 04:43:29,712 - telethon.network.mtprotosender - INFO - Disconnecting from **************:443/TcpFull...
2025-07-18 04:43:29,713 - telethon.network.mtprotosender - INFO - Disconnection from **************:443/TcpFull complete!
2025-07-18 04:43:29,715 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:43:31,284 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:43:31,736 - root - INFO - Code request sent successfully
2025-07-18 04:43:31,739 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:31] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:43:42,719 - root - ERROR - Failed to verify code: The asyncio event loop must not change after connection (see the FAQ for details)
2025-07-18 04:43:42,720 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:42] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:43:53,412 - root - ERROR - Failed to verify code: The asyncio event loop must not change after connection (see the FAQ for details)
2025-07-18 04:43:53,413 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:53] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:44:17,068 - root - ERROR - Failed to verify code: The asyncio event loop must not change after connection (see the FAQ for details)
2025-07-18 04:44:17,071 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:44:17] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:44:26,733 - root - ERROR - Failed to verify code: The asyncio event loop must not change after connection (see the FAQ for details)
2025-07-18 04:44:26,734 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:44:26] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:47:09,548 - telethon.crypto.libssl - INFO - Failed to load SSL library: <class 'OSError'> (no library called "ssl" found)
2025-07-18 04:47:09,550 - telethon.crypto.aes - INFO - cryptg module not installed and libssl not found, falling back to (slower) Python encryption
2025-07-18 04:47:10,324 - root - INFO - All dependencies are installed
2025-07-18 04:47:10,325 - root - INFO - Configuration is valid
2025-07-18 04:47:11,401 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:47:11,402 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:47:39,967 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:47:39] "GET / HTTP/1.1" 200 -
2025-07-18 04:47:40,059 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:47:40] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:47:40,121 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:47:40] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:47:42,110 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:47:42,172 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:47:42,450 - root - INFO - Connected to Telegram servers
2025-07-18 04:47:42,517 - root - INFO - Sending code request...
2025-07-18 04:47:42,603 - root - INFO - Code request sent successfully
2025-07-18 04:47:42,606 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:47:42] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:48:02,521 - root - INFO - Successfully authenticated with Telegram
2025-07-18 04:48:02,524 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:48:02] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:49:27,050 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:49:27,120 - root - ERROR - Error getting participants: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:49:27,123 - root - INFO - Extracted 0 members from Paid Udemy courses for free 2025
2025-07-18 04:49:27,129 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:49:27] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:49:59,066 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:49:59,178 - root - ERROR - Error getting participants: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:49:59,180 - root - INFO - Extracted 0 members from Paid Udemy courses for free 2025
2025-07-18 04:49:59,182 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:49:59] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:51:49,826 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:51:49,908 - root - ERROR - Error getting participants: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:51:49,910 - root - INFO - Extracted 0 members from Paid Udemy courses for free 2025
2025-07-18 04:51:49,911 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:51:49] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:56:34,654 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:56:34,655 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:57:07,988 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:07] "GET / HTTP/1.1" 200 -
2025-07-18 04:57:08,042 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:08] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:57:08,081 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:08] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:57:09,523 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:57:09,585 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:57:09,995 - root - INFO - Connected to Telegram servers
2025-07-18 04:57:09,996 - root - INFO - Already authorized
2025-07-18 04:57:09,997 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:09] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:57:13,023 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:57:13,023 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 04:57:13,182 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,185 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,187 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 04:57:13,270 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,273 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,274 - root - INFO - Attempting public group method...
2025-07-18 04:57:13,350 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,428 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,429 - root - WARNING - All extraction methods failed
2025-07-18 04:57:13,436 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:13] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:57:16,342 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:57:16,342 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 04:57:16,412 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,413 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,416 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 04:57:16,488 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,489 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,490 - root - INFO - Attempting public group method...
2025-07-18 04:57:16,572 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,650 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,658 - root - WARNING - All extraction methods failed
2025-07-18 04:57:16,660 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:16] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:59:41,049 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:59:41,050 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:00:01,694 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:01] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:00:04,195 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:04] "GET / HTTP/1.1" 200 -
2025-07-18 05:00:04,232 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:04] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:00:04,260 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:04] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:00:05,380 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:00:05,443 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:00:05,852 - root - INFO - Connected to Telegram servers
2025-07-18 05:00:05,853 - root - INFO - Already authorized
2025-07-18 05:00:05,856 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:05] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:00:08,393 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:00:08,393 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:00:08,510 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,511 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,511 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:00:08,631 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,632 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,632 - root - INFO - Attempting public group method...
2025-07-18 05:00:08,733 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,807 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,807 - root - INFO - Attempting message history method...
2025-07-18 05:00:08,808 - root - INFO - Scanning message history for active members...
2025-07-18 05:00:09,537 - root - ERROR - Error in message history method: 'Channel' object has no attribute 'bot'
2025-07-18 05:00:09,538 - root - WARNING - Message history method failed: 'Channel' object has no attribute 'bot'
2025-07-18 05:00:09,539 - root - INFO - Attempting recent activity method...
2025-07-18 05:00:09,614 - root - INFO - Attempting to get online members...
2025-07-18 05:00:09,685 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:09,687 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:00:14,215 - root - WARNING - Forwarded messages approach failed: 'Channel' object has no attribute 'bot'
2025-07-18 05:00:14,216 - root - WARNING - All extraction methods failed
2025-07-18 05:00:14,217 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:14] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:03:20,611 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:03:20,612 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:03:54,148 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:03:54] "GET / HTTP/1.1" 200 -
2025-07-18 05:03:54,203 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:03:54] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:03:54,257 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:03:54] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:03:55,362 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:03:55,426 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:03:55,813 - root - INFO - Connected to Telegram servers
2025-07-18 05:03:55,836 - root - INFO - Already authorized
2025-07-18 05:03:55,858 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:03:55] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:03:57,665 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:03:57,666 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:03:57,762 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:57,764 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:57,765 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:03:57,876 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:57,880 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:57,881 - root - INFO - Attempting public group method...
2025-07-18 05:03:58,035 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:58,231 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:58,232 - root - INFO - Attempting message history method...
2025-07-18 05:03:58,233 - root - INFO - Scanning message history for active members...
2025-07-18 05:04:00,146 - root - INFO - Scanned 100 messages, found 1 unique members
2025-07-18 05:04:02,126 - root - INFO - Scanned 200 messages, found 1 unique members
2025-07-18 05:04:02,130 - root - INFO - Message history scan complete: 1 members from 200 messages
2025-07-18 05:04:02,131 - root - INFO - Successfully extracted 1 members using message history method
2025-07-18 05:04:02,149 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:04:02] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:09:09,156 - telethon.client.updates - INFO - Got difference for account updates
2025-07-18 05:09:53,407 - root - INFO - Adding members to group: Inc
2025-07-18 05:09:53,493 - root - ERROR - Failed to add user udemy2020: Cannot cast InputPeerChannel to any kind of InputUser.
2025-07-18 05:09:53,494 - root - INFO - Member addition complete. Added: 0, Failed: 1
2025-07-18 05:09:53,495 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:09:53] "POST /api/transfer-members HTTP/1.1" 200 -
2025-07-18 05:12:12,428 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:12:12,428 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:12:56,959 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:12:56] "GET / HTTP/1.1" 200 -
2025-07-18 05:12:57,010 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:12:57] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:12:57,047 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:12:57] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:12:58,307 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:12:58,372 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:12:58,739 - root - INFO - Connected to Telegram servers
2025-07-18 05:12:58,740 - root - INFO - Already authorized
2025-07-18 05:12:58,741 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:12:58] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:13:00,322 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:13:00,322 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:13:00,400 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,408 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,409 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:13:00,479 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,480 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,481 - root - INFO - Attempting public group method...
2025-07-18 05:13:00,551 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,623 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,624 - root - INFO - Attempting message history method...
2025-07-18 05:13:00,624 - root - INFO - Scanning message history for active members...
2025-07-18 05:13:02,522 - root - INFO - Scanned 100 messages, found 0 unique members
2025-07-18 05:13:04,458 - root - INFO - Scanned 200 messages, found 0 unique members
2025-07-18 05:13:04,459 - root - INFO - Message history scan complete: 0 members from 200 messages
2025-07-18 05:13:04,460 - root - INFO - Attempting recent activity method...
2025-07-18 05:13:04,537 - root - INFO - Attempting to get online members...
2025-07-18 05:13:04,616 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:04,621 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:13:30,263 - root - ERROR - Error extracting members: 
2025-07-18 05:13:30,265 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:13:30] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:14:50,574 - root - WARNING - All extraction methods failed
2025-07-18 05:15:32,434 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:15:32,435 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:15:46,571 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:15:46] "GET / HTTP/1.1" 200 -
2025-07-18 05:15:46,637 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:15:46] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:15:46,729 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:15:46] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:15:47,788 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:15:47,852 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:15:48,253 - root - INFO - Connected to Telegram servers
2025-07-18 05:15:48,253 - root - INFO - Already authorized
2025-07-18 05:15:48,254 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:15:48] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:15:49,998 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:15:49,998 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:15:50,070 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:50,070 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:50,071 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:15:50,152 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:50,153 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:50,154 - root - INFO - Attempting public group method...
2025-07-18 05:15:50,230 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:50,327 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:50,328 - root - INFO - Attempting message history method...
2025-07-18 05:15:50,329 - root - INFO - Scanning message history for active members...
2025-07-18 05:15:51,313 - root - INFO - Message 1: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,314 - root - INFO - Message 2: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,314 - root - INFO - Message 3: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,314 - root - INFO - Message 4: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,315 - root - INFO - Message 5: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,315 - root - INFO - Message 6: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,315 - root - INFO - Message 7: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,316 - root - INFO - Message 8: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,316 - root - INFO - Message 9: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:51,316 - root - INFO - Message 10: Sender type: Channel, ID: 1215683262
2025-07-18 05:15:52,825 - root - INFO - Scanned 100 messages, found 0 unique members
2025-07-18 05:15:56,133 - root - INFO - Scanned 200 messages, found 0 unique members
2025-07-18 05:15:56,642 - root - INFO - Message history scan complete: 0 members from 200 messages
2025-07-18 05:15:56,643 - root - INFO - Attempting recent activity method...
2025-07-18 05:15:56,719 - root - INFO - Attempting to get online members...
2025-07-18 05:15:56,801 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:15:56,802 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:15:58,442 - root - INFO - Scanned 50 forwarded/reply messages, found 0 members
2025-07-18 05:15:58,982 - root - INFO - Scanned 100 forwarded/reply messages, found 0 members
2025-07-18 05:16:00,120 - root - INFO - Scanned 150 forwarded/reply messages, found 0 members
2025-07-18 05:16:00,625 - root - INFO - Scanned 200 forwarded/reply messages, found 0 members
2025-07-18 05:16:00,627 - root - WARNING - All extraction methods failed
2025-07-18 05:16:00,632 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:16:00] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:18:13,247 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:18:13] "GET / HTTP/1.1" 200 -
2025-07-18 05:18:13,299 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:18:13] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:18:13,344 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:18:13] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:18:14,891 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:18:14,892 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:18:14,966 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:14,967 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:14,967 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:18:15,044 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:15,045 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:15,045 - root - INFO - Attempting public group method...
2025-07-18 05:18:15,127 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:15,199 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:15,200 - root - INFO - Attempting message history method...
2025-07-18 05:18:15,200 - root - INFO - Scanning message history for active members...
2025-07-18 05:18:15,965 - root - INFO - Message 1: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,965 - root - INFO - Message 2: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,966 - root - INFO - Message 3: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,966 - root - INFO - Message 4: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,966 - root - INFO - Message 5: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,967 - root - INFO - Message 6: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,967 - root - INFO - Message 7: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,970 - root - INFO - Message 8: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,972 - root - INFO - Message 9: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:15,972 - root - INFO - Message 10: Sender type: Channel, ID: 1215683262
2025-07-18 05:18:17,493 - root - INFO - Scanned 100 messages, found 0 unique members
2025-07-18 05:18:20,393 - root - INFO - Scanned 200 messages, found 0 unique members
2025-07-18 05:18:20,897 - root - INFO - Message history scan complete: 0 members from 200 messages
2025-07-18 05:18:20,898 - root - INFO - Attempting recent activity method...
2025-07-18 05:18:21,014 - root - INFO - Attempting to get online members...
2025-07-18 05:18:21,199 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:18:21,199 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:18:22,496 - root - INFO - Scanned 50 forwarded/reply messages, found 0 members
2025-07-18 05:18:23,014 - root - INFO - Scanned 100 forwarded/reply messages, found 0 members
2025-07-18 05:18:24,130 - root - INFO - Scanned 150 forwarded/reply messages, found 0 members
2025-07-18 05:18:24,634 - root - INFO - Scanned 200 forwarded/reply messages, found 0 members
2025-07-18 05:18:24,636 - root - WARNING - All extraction methods failed
2025-07-18 05:18:24,639 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:18:24] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:20:52,238 - telethon.client.updates - INFO - Got difference for account updates
2025-07-18 05:22:13,077 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:22:13,077 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:22:24,115 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:22:24] "GET / HTTP/1.1" 200 -
2025-07-18 05:22:24,168 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:22:24] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:22:24,213 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:22:24] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:22:25,316 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:22:25,387 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:22:25,778 - root - INFO - Connected to Telegram servers
2025-07-18 05:22:25,778 - root - INFO - Already authorized
2025-07-18 05:22:25,780 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:22:25] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:22:28,078 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:22:28,079 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:22:28,150 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:28,151 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:28,159 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:22:28,238 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:28,241 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:28,243 - root - INFO - Attempting public group method...
2025-07-18 05:22:28,314 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:28,394 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:28,395 - root - INFO - Attempting message history method...
2025-07-18 05:22:28,395 - root - INFO - Scanning message history for active members...
2025-07-18 05:22:29,566 - root - INFO - Message 1: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,567 - root - INFO - Message 2: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,567 - root - INFO - Message 3: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,567 - root - INFO - Message 4: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,573 - root - INFO - Message 5: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,574 - root - INFO - Message 6: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,574 - root - INFO - Message 7: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,575 - root - INFO - Message 8: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,575 - root - INFO - Message 9: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:29,575 - root - INFO - Message 10: Sender type: Channel, ID: 1215683262
2025-07-18 05:22:31,087 - root - INFO - Scanned 100 messages, found 0 unique members
2025-07-18 05:22:34,165 - root - INFO - Scanned 200 messages, found 0 unique members
2025-07-18 05:22:34,681 - root - INFO - Message history scan complete: 0 members from 200 messages
2025-07-18 05:22:34,682 - root - INFO - Attempting recent activity method...
2025-07-18 05:22:34,759 - root - INFO - Attempting to get online members...
2025-07-18 05:22:34,847 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:34,848 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:22:36,540 - root - INFO - Scanned 50 forwarded/reply messages, found 0 members
2025-07-18 05:22:37,045 - root - INFO - Scanned 100 forwarded/reply messages, found 0 members
2025-07-18 05:22:38,411 - root - INFO - Scanned 150 forwarded/reply messages, found 0 members
2025-07-18 05:22:38,919 - root - INFO - Scanned 200 forwarded/reply messages, found 0 members
2025-07-18 05:22:38,921 - root - INFO - Attempting admin extraction method...
2025-07-18 05:22:38,922 - root - INFO - Attempting to extract group admins...
2025-07-18 05:22:39,014 - root - WARNING - Direct admin extraction failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:22:39,021 - root - INFO - Scanning for admin signatures in messages...
2025-07-18 05:22:42,871 - root - INFO - Admin extraction complete: 0 admins found
2025-07-18 05:22:42,872 - root - INFO - Attempting reaction/view method for channels...
2025-07-18 05:22:42,873 - root - INFO - Scanning message reactions for users...
2025-07-18 05:22:58,008 - root - ERROR - Error extracting members: 
2025-07-18 05:22:58,026 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:22:58] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:23:14,333 - root - INFO - Reaction scan complete: 0 members from 100 messages
2025-07-18 05:23:14,334 - root - INFO - Attempting comment extraction method...
2025-07-18 05:23:14,335 - root - INFO - Scanning channel comments for users...
2025-07-18 05:23:14,749 - root - INFO - Message 4110 has 0 replies
2025-07-18 05:23:15,156 - root - INFO - Message 4101 has 0 replies
2025-07-18 05:23:15,578 - root - INFO - Message 3725 has 0 replies
2025-07-18 05:23:16,000 - root - INFO - Message 3724 has 0 replies
2025-07-18 05:23:16,419 - root - INFO - Message 3723 has 0 replies
2025-07-18 05:23:16,888 - root - INFO - Message 3722 has 2 replies
2025-07-18 05:23:17,321 - root - INFO - Message 3721 has 0 replies
2025-07-18 05:23:17,731 - root - INFO - Message 3718 has 0 replies
2025-07-18 05:23:18,156 - root - INFO - Message 3717 has 7 replies
2025-07-18 05:23:18,577 - root - INFO - Message 3716 has 3 replies
2025-07-18 05:23:18,986 - root - INFO - Message 3715 has 2 replies
2025-07-18 05:23:19,422 - root - INFO - Message 3713 has 6 replies
2025-07-18 05:23:19,849 - root - INFO - Message 3711 has 2 replies
2025-07-18 05:23:20,254 - root - INFO - Message 3710 has 2 replies
2025-07-18 05:23:20,722 - root - INFO - Message 3709 has 0 replies
2025-07-18 05:23:21,160 - root - INFO - Message 3708 has 0 replies
2025-07-18 05:23:21,581 - root - INFO - Message 3707 has 1 replies
2025-07-18 05:23:22,050 - root - INFO - Message 3706 has 2 replies
2025-07-18 05:23:22,614 - root - INFO - Message 3705 has 0 replies
2025-07-18 05:23:23,333 - root - INFO - Message 3703 has 3 replies
2025-07-18 05:23:23,741 - root - INFO - Message 3702 has 1 replies
2025-07-18 05:23:24,165 - root - INFO - Message 3700 has 1 replies
2025-07-18 05:23:24,572 - root - INFO - Message 3699 has 1 replies
2025-07-18 05:23:24,979 - root - INFO - Message 3698 has 0 replies
2025-07-18 05:23:25,383 - root - INFO - Message 3697 has 0 replies
2025-07-18 05:23:25,812 - root - INFO - Message 3696 has 1 replies
2025-07-18 05:23:26,223 - root - INFO - Message 3694 has 0 replies
2025-07-18 05:23:26,644 - root - INFO - Message 3693 has 0 replies
2025-07-18 05:23:27,048 - root - INFO - Message 3692 has 1 replies
2025-07-18 05:23:27,457 - root - INFO - Message 3690 has 2 replies
2025-07-18 05:23:27,865 - root - INFO - Message 3689 has 1 replies
2025-07-18 05:23:28,288 - root - INFO - Message 3687 has 1 replies
2025-07-18 05:23:28,709 - root - INFO - Message 3686 has 3 replies
2025-07-18 05:23:29,130 - root - INFO - Message 3685 has 2 replies
2025-07-18 05:23:29,550 - root - INFO - Message 3684 has 4 replies
2025-07-18 05:23:29,943 - root - INFO - Message 3683 has 10 replies
2025-07-18 05:23:30,365 - root - INFO - Message 3682 has 5 replies
2025-07-18 05:23:30,786 - root - INFO - Message 3681 has 1 replies
2025-07-18 05:23:31,302 - root - INFO - Message 3680 has 0 replies
2025-07-18 05:23:31,723 - root - INFO - Message 3679 has 0 replies
2025-07-18 05:23:32,146 - root - INFO - Message 3678 has 4 replies
2025-07-18 05:23:32,557 - root - INFO - Message 3677 has 6 replies
2025-07-18 05:23:32,967 - root - INFO - Message 3676 has 7 replies
2025-07-18 05:23:33,358 - root - INFO - Message 3675 has 1 replies
2025-07-18 05:23:33,909 - root - INFO - Message 3674 has 2 replies
2025-07-18 05:23:34,380 - root - INFO - Message 3673 has 1 replies
2025-07-18 05:23:35,763 - root - INFO - Comment scan complete: 0 members from 50 messages
2025-07-18 05:23:35,764 - root - WARNING - All extraction methods failed
2025-07-18 05:23:35,765 - root - INFO - Suggestion: Try a different group with more user interaction (discussion groups rather than broadcast channels)
