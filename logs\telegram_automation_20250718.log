2025-07-18 04:37:11,491 - telethon.crypto.libssl - INFO - Failed to load SSL library: <class 'OSError'> (no library called "ssl" found)
2025-07-18 04:37:11,493 - telethon.crypto.aes - INFO - cryptg module not installed and libssl not found, falling back to (slower) Python encryption
2025-07-18 04:37:12,537 - root - INFO - All dependencies are installed
2025-07-18 04:37:12,538 - root - INFO - Configuration is valid
2025-07-18 04:37:12,539 - root - INFO - Created directory: sessions
2025-07-18 04:37:12,606 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:37:12,607 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:37:32,853 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:32] "GET / HTTP/1.1" 200 -
2025-07-18 04:37:32,964 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:32] "GET /static/app.js HTTP/1.1" 200 -
2025-07-18 04:37:33,592 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:33] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:37:33,810 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:33] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-18 04:37:36,516 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:37:38,185 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:37:38,602 - root - ERROR - Failed to initialize Telegram client: The api_id/api_hash combination is invalid (caused by SendCodeRequest)
2025-07-18 04:37:38,604 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:38] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:37:46,212 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:37:46,279 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:37:47,003 - root - ERROR - Failed to initialize Telegram client: The api_id/api_hash combination is invalid (caused by SendCodeRequest)
2025-07-18 04:37:47,007 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:47] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:39:50,790 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:50] "GET / HTTP/1.1" 200 -
2025-07-18 04:39:50,952 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:50] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:39:51,135 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:51] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:39:53,828 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:39:53,894 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:39:54,295 - root - ERROR - Failed to initialize Telegram client: The api_id/api_hash combination is invalid (caused by SendCodeRequest)
2025-07-18 04:39:54,297 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:54] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:42:48,179 - telethon.crypto.libssl - INFO - Failed to load SSL library: <class 'OSError'> (no library called "ssl" found)
2025-07-18 04:42:48,181 - telethon.crypto.aes - INFO - cryptg module not installed and libssl not found, falling back to (slower) Python encryption
2025-07-18 04:42:49,077 - root - INFO - All dependencies are installed
2025-07-18 04:42:49,077 - root - INFO - Configuration is valid
2025-07-18 04:42:49,137 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:42:49,138 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:43:24,194 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:24] "GET / HTTP/1.1" 200 -
2025-07-18 04:43:24,257 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:24] "GET /static/app.js HTTP/1.1" 200 -
2025-07-18 04:43:24,330 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:24] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:43:26,081 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:26] "GET / HTTP/1.1" 200 -
2025-07-18 04:43:26,139 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:26] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:43:26,161 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:26] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:43:27,722 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:43:29,255 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:43:29,486 - root - INFO - Connected to Telegram servers
2025-07-18 04:43:29,555 - root - INFO - Sending code request...
2025-07-18 04:43:29,628 - telethon.client.users - INFO - Phone migrated to 4
2025-07-18 04:43:29,629 - telethon.client.telegrambaseclient - INFO - Reconnecting to new data center 4
2025-07-18 04:43:29,712 - telethon.network.mtprotosender - INFO - Disconnecting from **************:443/TcpFull...
2025-07-18 04:43:29,713 - telethon.network.mtprotosender - INFO - Disconnection from **************:443/TcpFull complete!
2025-07-18 04:43:29,715 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:43:31,284 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:43:31,736 - root - INFO - Code request sent successfully
2025-07-18 04:43:31,739 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:31] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:43:42,719 - root - ERROR - Failed to verify code: The asyncio event loop must not change after connection (see the FAQ for details)
2025-07-18 04:43:42,720 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:42] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:43:53,412 - root - ERROR - Failed to verify code: The asyncio event loop must not change after connection (see the FAQ for details)
2025-07-18 04:43:53,413 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:43:53] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:44:17,068 - root - ERROR - Failed to verify code: The asyncio event loop must not change after connection (see the FAQ for details)
2025-07-18 04:44:17,071 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:44:17] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:44:26,733 - root - ERROR - Failed to verify code: The asyncio event loop must not change after connection (see the FAQ for details)
2025-07-18 04:44:26,734 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:44:26] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:47:09,548 - telethon.crypto.libssl - INFO - Failed to load SSL library: <class 'OSError'> (no library called "ssl" found)
2025-07-18 04:47:09,550 - telethon.crypto.aes - INFO - cryptg module not installed and libssl not found, falling back to (slower) Python encryption
2025-07-18 04:47:10,324 - root - INFO - All dependencies are installed
2025-07-18 04:47:10,325 - root - INFO - Configuration is valid
2025-07-18 04:47:11,401 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:47:11,402 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:47:39,967 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:47:39] "GET / HTTP/1.1" 200 -
2025-07-18 04:47:40,059 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:47:40] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:47:40,121 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:47:40] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:47:42,110 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:47:42,172 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:47:42,450 - root - INFO - Connected to Telegram servers
2025-07-18 04:47:42,517 - root - INFO - Sending code request...
2025-07-18 04:47:42,603 - root - INFO - Code request sent successfully
2025-07-18 04:47:42,606 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:47:42] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:48:02,521 - root - INFO - Successfully authenticated with Telegram
2025-07-18 04:48:02,524 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:48:02] "POST /api/verify-code HTTP/1.1" 200 -
2025-07-18 04:49:27,050 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:49:27,120 - root - ERROR - Error getting participants: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:49:27,123 - root - INFO - Extracted 0 members from Paid Udemy courses for free 2025
2025-07-18 04:49:27,129 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:49:27] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:49:59,066 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:49:59,178 - root - ERROR - Error getting participants: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:49:59,180 - root - INFO - Extracted 0 members from Paid Udemy courses for free 2025
2025-07-18 04:49:59,182 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:49:59] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:51:49,826 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:51:49,908 - root - ERROR - Error getting participants: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:51:49,910 - root - INFO - Extracted 0 members from Paid Udemy courses for free 2025
2025-07-18 04:51:49,911 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:51:49] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:56:34,654 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:56:34,655 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:57:07,988 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:07] "GET / HTTP/1.1" 200 -
2025-07-18 04:57:08,042 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:08] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:57:08,081 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:08] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:57:09,523 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:57:09,585 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:57:09,995 - root - INFO - Connected to Telegram servers
2025-07-18 04:57:09,996 - root - INFO - Already authorized
2025-07-18 04:57:09,997 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:09] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:57:13,023 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:57:13,023 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 04:57:13,182 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,185 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,187 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 04:57:13,270 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,273 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,274 - root - INFO - Attempting public group method...
2025-07-18 04:57:13,350 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,428 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:13,429 - root - WARNING - All extraction methods failed
2025-07-18 04:57:13,436 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:13] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:57:16,342 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 04:57:16,342 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 04:57:16,412 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,413 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,416 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 04:57:16,488 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,489 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,490 - root - INFO - Attempting public group method...
2025-07-18 04:57:16,572 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,650 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 04:57:16,658 - root - WARNING - All extraction methods failed
2025-07-18 04:57:16,660 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:57:16] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 04:59:41,049 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:59:41,050 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:00:01,694 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:01] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:00:04,195 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:04] "GET / HTTP/1.1" 200 -
2025-07-18 05:00:04,232 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:04] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:00:04,260 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:04] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:00:05,380 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:00:05,443 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:00:05,852 - root - INFO - Connected to Telegram servers
2025-07-18 05:00:05,853 - root - INFO - Already authorized
2025-07-18 05:00:05,856 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:05] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:00:08,393 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:00:08,393 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:00:08,510 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,511 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,511 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:00:08,631 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,632 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,632 - root - INFO - Attempting public group method...
2025-07-18 05:00:08,733 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,807 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:08,807 - root - INFO - Attempting message history method...
2025-07-18 05:00:08,808 - root - INFO - Scanning message history for active members...
2025-07-18 05:00:09,537 - root - ERROR - Error in message history method: 'Channel' object has no attribute 'bot'
2025-07-18 05:00:09,538 - root - WARNING - Message history method failed: 'Channel' object has no attribute 'bot'
2025-07-18 05:00:09,539 - root - INFO - Attempting recent activity method...
2025-07-18 05:00:09,614 - root - INFO - Attempting to get online members...
2025-07-18 05:00:09,685 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:00:09,687 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:00:14,215 - root - WARNING - Forwarded messages approach failed: 'Channel' object has no attribute 'bot'
2025-07-18 05:00:14,216 - root - WARNING - All extraction methods failed
2025-07-18 05:00:14,217 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:00:14] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:03:20,611 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:03:20,612 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:03:54,148 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:03:54] "GET / HTTP/1.1" 200 -
2025-07-18 05:03:54,203 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:03:54] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:03:54,257 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:03:54] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:03:55,362 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:03:55,426 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:03:55,813 - root - INFO - Connected to Telegram servers
2025-07-18 05:03:55,836 - root - INFO - Already authorized
2025-07-18 05:03:55,858 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:03:55] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:03:57,665 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:03:57,666 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:03:57,762 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:57,764 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:57,765 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:03:57,876 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:57,880 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:57,881 - root - INFO - Attempting public group method...
2025-07-18 05:03:58,035 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:58,231 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:03:58,232 - root - INFO - Attempting message history method...
2025-07-18 05:03:58,233 - root - INFO - Scanning message history for active members...
2025-07-18 05:04:00,146 - root - INFO - Scanned 100 messages, found 1 unique members
2025-07-18 05:04:02,126 - root - INFO - Scanned 200 messages, found 1 unique members
2025-07-18 05:04:02,130 - root - INFO - Message history scan complete: 1 members from 200 messages
2025-07-18 05:04:02,131 - root - INFO - Successfully extracted 1 members using message history method
2025-07-18 05:04:02,149 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:04:02] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:09:09,156 - telethon.client.updates - INFO - Got difference for account updates
2025-07-18 05:09:53,407 - root - INFO - Adding members to group: Inc
2025-07-18 05:09:53,493 - root - ERROR - Failed to add user udemy2020: Cannot cast InputPeerChannel to any kind of InputUser.
2025-07-18 05:09:53,494 - root - INFO - Member addition complete. Added: 0, Failed: 1
2025-07-18 05:09:53,495 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:09:53] "POST /api/transfer-members HTTP/1.1" 200 -
2025-07-18 05:12:12,428 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 05:12:12,428 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 05:12:56,959 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:12:56] "GET / HTTP/1.1" 200 -
2025-07-18 05:12:57,010 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:12:57] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 05:12:57,047 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:12:57] "GET /api/status HTTP/1.1" 200 -
2025-07-18 05:12:58,307 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 05:12:58,372 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 05:12:58,739 - root - INFO - Connected to Telegram servers
2025-07-18 05:12:58,740 - root - INFO - Already authorized
2025-07-18 05:12:58,741 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:12:58] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 05:13:00,322 - root - INFO - Extracting members from group: Paid Udemy courses for free 2025
2025-07-18 05:13:00,322 - root - INFO - Attempting admin method (GetParticipantsRequest)...
2025-07-18 05:13:00,400 - root - ERROR - Error in admin method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,408 - root - WARNING - Admin method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,409 - root - INFO - Attempting aggressive method (iter_participants)...
2025-07-18 05:13:00,479 - root - ERROR - Error in aggressive method: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,480 - root - WARNING - Aggressive method failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,481 - root - INFO - Attempting public group method...
2025-07-18 05:13:00,551 - root - WARNING - Filter ChannelParticipantsRecent() failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,623 - root - WARNING - Filter ChannelParticipantsSearch(q='') failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:00,624 - root - INFO - Attempting message history method...
2025-07-18 05:13:00,624 - root - INFO - Scanning message history for active members...
2025-07-18 05:13:02,522 - root - INFO - Scanned 100 messages, found 0 unique members
2025-07-18 05:13:04,458 - root - INFO - Scanned 200 messages, found 0 unique members
2025-07-18 05:13:04,459 - root - INFO - Message history scan complete: 0 members from 200 messages
2025-07-18 05:13:04,460 - root - INFO - Attempting recent activity method...
2025-07-18 05:13:04,537 - root - INFO - Attempting to get online members...
2025-07-18 05:13:04,616 - root - WARNING - Online members approach failed: Chat admin privileges are required to do that in the specified chat (for example, to send a message in a channel which is not yours), or invalid permissions used for the channel or group (caused by GetParticipantsRequest)
2025-07-18 05:13:04,621 - root - INFO - Scanning for forwarded messages and replies...
2025-07-18 05:13:30,263 - root - ERROR - Error extracting members: 
2025-07-18 05:13:30,265 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 05:13:30] "POST /api/extract-members HTTP/1.1" 200 -
2025-07-18 05:14:50,574 - root - WARNING - All extraction methods failed
