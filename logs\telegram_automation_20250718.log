2025-07-18 04:37:11,491 - telethon.crypto.libssl - INFO - Failed to load SSL library: <class 'OSError'> (no library called "ssl" found)
2025-07-18 04:37:11,493 - telethon.crypto.aes - INFO - cryptg module not installed and libssl not found, falling back to (slower) Python encryption
2025-07-18 04:37:12,537 - root - INFO - All dependencies are installed
2025-07-18 04:37:12,538 - root - INFO - Configuration is valid
2025-07-18 04:37:12,539 - root - INFO - Created directory: sessions
2025-07-18 04:37:12,606 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:37:12,607 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 04:37:32,853 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:32] "GET / HTTP/1.1" 200 -
2025-07-18 04:37:32,964 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:32] "GET /static/app.js HTTP/1.1" 200 -
2025-07-18 04:37:33,592 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:33] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:37:33,810 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:33] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-18 04:37:36,516 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:37:38,185 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:37:38,602 - root - ERROR - Failed to initialize Telegram client: The api_id/api_hash combination is invalid (caused by SendCodeRequest)
2025-07-18 04:37:38,604 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:38] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:37:46,212 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:37:46,279 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:37:47,003 - root - ERROR - Failed to initialize Telegram client: The api_id/api_hash combination is invalid (caused by SendCodeRequest)
2025-07-18 04:37:47,007 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:37:47] "POST /api/initialize HTTP/1.1" 200 -
2025-07-18 04:39:50,790 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:50] "GET / HTTP/1.1" 200 -
2025-07-18 04:39:50,952 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:50] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-07-18 04:39:51,135 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:51] "GET /api/status HTTP/1.1" 200 -
2025-07-18 04:39:53,828 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-07-18 04:39:53,894 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-07-18 04:39:54,295 - root - ERROR - Failed to initialize Telegram client: The api_id/api_hash combination is invalid (caused by SendCodeRequest)
2025-07-18 04:39:54,297 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 04:39:54] "POST /api/initialize HTTP/1.1" 200 -
