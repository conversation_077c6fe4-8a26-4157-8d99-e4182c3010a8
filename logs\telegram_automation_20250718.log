2025-07-18 04:37:11,491 - telethon.crypto.libssl - INFO - Failed to load SSL library: <class 'OSError'> (no library called "ssl" found)
2025-07-18 04:37:11,493 - telethon.crypto.aes - INFO - cryptg module not installed and libssl not found, falling back to (slower) Python encryption
2025-07-18 04:37:12,537 - root - INFO - All dependencies are installed
2025-07-18 04:37:12,538 - root - INFO - Configuration is valid
2025-07-18 04:37:12,539 - root - INFO - Created directory: sessions
2025-07-18 04:37:12,606 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-18 04:37:12,607 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
