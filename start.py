#!/usr/bin/env python3
"""
Telegram Group Manager Startup Script
"""

import os
import sys
import subprocess
from config import Config
from logger import logger

def check_dependencies():
    """Check if all required dependencies are installed"""
    try:
        import telethon
        import flask
        import dotenv
        logger.info("All dependencies are installed")
        return True
    except ImportError as e:
        logger.error(f"Missing dependency: {e}")
        print(f"Error: Missing dependency - {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_configuration():
    """Check if configuration is valid"""
    try:
        if not os.path.exists('.env'):
            logger.error("Configuration file .env not found")
            print("Error: Configuration file .env not found")
            print("Please copy .env.example to .env and configure your settings")
            return False
        
        Config.validate()
        logger.info("Configuration is valid")
        return True
    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        print(f"Configuration error: {e}")
        print("Please check your .env file")
        return False

def create_directories():
    """Create necessary directories"""
    directories = ['logs', 'sessions']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            logger.info(f"Created directory: {directory}")

def main():
    """Main startup function"""
    print("=" * 50)
    print("Telegram Group Manager")
    print("=" * 50)
    
    # Check dependencies
    print("Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    
    # Check configuration
    print("Checking configuration...")
    if not check_configuration():
        sys.exit(1)
    
    # Create directories
    print("Creating directories...")
    create_directories()
    
    # Start the application
    print(f"Starting application on http://{Config.FLASK_HOST}:{Config.FLASK_PORT}")
    print("Press Ctrl+C to stop the application")
    print("=" * 50)
    
    try:
        from app import app
        app.run(
            host=Config.FLASK_HOST,
            port=Config.FLASK_PORT,
            debug=Config.FLASK_DEBUG
        )
    except KeyboardInterrupt:
        print("\nApplication stopped by user")
        logger.info("Application stopped by user")
    except Exception as e:
        print(f"Error starting application: {e}")
        logger.error(f"Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
