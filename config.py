import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    # Telegram API Configuration
    API_ID = int(os.getenv('API_ID', 0))
    API_HASH = os.getenv('API_HASH', '')
    PHONE_NUMBER = os.getenv('PHONE_NUMBER', '')
    SESSION_NAME = os.getenv('SESSION_NAME', 'telegram_automation')
    
    # Rate limiting settings
    RATE_LIMIT_MESSAGES = int(os.getenv('RATE_LIMIT_MESSAGES', 20))
    RATE_LIMIT_MEMBERS = int(os.getenv('RATE_LIMIT_MEMBERS', 50))
    
    # Web interface settings
    FLASK_HOST = os.getenv('FLASK_HOST', '0.0.0.0')
    FLASK_PORT = int(os.getenv('FLASK_PORT', 5000))
    FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    # Logging configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    
    # Validation
    @classmethod
    def validate(cls):
        errors = []
        if not cls.API_ID:
            errors.append("API_ID is required")
        if not cls.API_HASH:
            errors.append("API_HASH is required")
        if not cls.PHONE_NUMBER:
            errors.append("PHONE_NUMBER is required")
        
        if errors:
            raise ValueError("Configuration errors: " + ", ".join(errors))
        
        return True
